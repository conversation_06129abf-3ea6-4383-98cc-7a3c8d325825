import { Future } from "../../types/module";
import { ExecutionState } from "../execution/types/execution-state";
import { ReconciliationContext, ReconciliationFutureResult } from "./types";
export declare function reconcileFutureSpecificReconciliations(future: Future, executionState: ExecutionState, context: ReconciliationContext): Promise<ReconciliationFutureResult>;
//# sourceMappingURL=reconcile-future-specific-reconciliations.d.ts.map