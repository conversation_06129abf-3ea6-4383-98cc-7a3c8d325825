{"version": 3, "file": "WebCryptoP256.d.ts", "sourceRoot": "", "sources": ["../../core/WebCryptoP256.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AACpC,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAElD;;;;;;;;;;;;;;;;;;;;;;;;GAwBG;AACH,wBAAsB,aAAa,CACjC,OAAO,GAAE,aAAa,CAAC,OAAY,GAClC,OAAO,CAAC,aAAa,CAAC,UAAU,CAAC,CAmBnC;AAED,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,KAAK,OAAO,GAAG;QACb,qIAAqI;QACrI,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAClC,CAAA;IAED,KAAK,UAAU,GAAG,OAAO,CAAC;QACxB,UAAU,EAAE,SAAS,CAAA;QACrB,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;KAC/B,CAAC,CAAA;IAEF,KAAK,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACnE;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAsB,IAAI,CACxB,OAAO,EAAE,IAAI,CAAC,OAAO,GACpB,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAerC;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,GAAG;QACb,uBAAuB;QACvB,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;QAC9B,yBAAyB;QACzB,UAAU,EAAE,SAAS,CAAA;KACtB,CAAA;IAED,KAAK,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACpE;AAED;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,wBAAsB,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,CAoBtE;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,OAAO,GAAG;QACb,0CAA0C;QAC1C,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QACvC,gCAAgC;QAChC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrC,+BAA+B;QAC/B,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,KAAK,CAAC,KAAK,CAAA;KAC/B,CAAA;IAED,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC"}