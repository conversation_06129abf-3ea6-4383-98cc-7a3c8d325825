{"version": 3, "file": "WebAuthnP256.js", "sourceRoot": "", "sources": ["../../core/WebAuthnP256.ts"], "names": [], "mappings": ";;;AAsDA,4CA6BC;AAkDD,oDAQC;AA0CD,8CAeC;AAiCD,oEAiDC;AA0FD,kEA+BC;AAiED,wCAyCC;AAwED,oBA0CC;AAyDD,wBAwDC;AA9tBD,sCAAqC;AACrC,oCAAmC;AACnC,sCAAqC;AACrC,kCAAiC;AACjC,gCAA+B;AAC/B,kCAAiC;AAIjC,mDAAkD;AAkBrC,QAAA,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;IAC7C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;CACzE,CAAC,CAAA;AAyBK,KAAK,UAAU,gBAAgB,CACpC,OAAiC;IAEjC,MAAM,EACJ,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CACjD,MAAM,CAAC,SAAS,CAAC,WAAW,CAC7B,EACD,GAAG,IAAI,EACR,GAAG,OAAO,CAAA;IACX,MAAM,eAAe,GAAG,4BAA4B,CAAC,IAAI,CAAC,CAAA;IAC1D,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,CAAC,MAAM,QAAQ,CAChC,eAAe,CAChB,CAAiC,CAAA;QAClC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,6BAA6B,EAAE,CAAA;QAE1D,MAAM,QAAQ,GAAG,UAAU,CAAC,QAA4C,CAAA;QACxE,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAA;QAEnE,OAAO;YACL,EAAE,EAAE,UAAU,CAAC,EAAE;YACjB,SAAS;YACT,GAAG,EAAE,UAAU;SAChB,CAAA;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,6BAA6B,CAAC;YACtC,KAAK,EAAE,KAAc;SACtB,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAkDD,SAAgB,oBAAoB,CAClC,UAAwC,EAAE;IAE1C,MAAM,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAAE,SAAS,GAAG,CAAC,EAAE,GAAG,OAAO,CAAA;IAC5E,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAA;IAClD,MAAM,UAAU,GAAG,GAAG,CAAC,UAAU,CAAC,IAAI,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAA;IACpD,MAAM,eAAe,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAA;IAC9D,OAAO,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,UAAU,EAAE,eAAe,CAAC,CAAA;AAC1D,CAAC;AA0CD,SAAgB,iBAAiB,CAAC,OAAkC;IAClE,MAAM,EACJ,SAAS,EACT,WAAW,GAAG,KAAK,EACnB,eAAe,EACf,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,MAAM,GAChC,GAAG,OAAO,CAAA;IAEX,OAAO,IAAI,CAAC,SAAS,CAAC;QACpB,IAAI,EAAE,cAAc;QACpB,SAAS,EAAE,MAAM,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,CAAC;QAC/D,MAAM;QACN,WAAW;QACX,GAAG,eAAe;KACnB,CAAC,CAAA;AACJ,CAAC;AAiCD,SAAgB,4BAA4B,CAC1C,OAA6C;IAE7C,MAAM,EACJ,WAAW,GAAG,MAAM,EACpB,sBAAsB,GAAG;QACvB,WAAW,EAAE,WAAW;QACxB,kBAAkB,EAAE,KAAK;QACzB,gBAAgB,EAAE,UAAU;KAC7B,EACD,SAAS,GAAG,uBAAe,EAC3B,oBAAoB,EACpB,IAAI,EAAE,KAAK,EACX,EAAE,GAAG;QACH,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,QAAQ;QAC5B,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,KAAK;KAC5B,EACD,IAAI,EACJ,UAAU,GACX,GAAG,OAAO,CAAA;IACX,MAAM,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,IAAI,KAAK,CAAE,CAAA;IACnC,OAAO;QACL,SAAS,EAAE;YACT,WAAW;YACX,sBAAsB;YACtB,SAAS;YACT,GAAG,CAAC,oBAAoB;gBACtB,CAAC,CAAC;oBACE,kBAAkB,EAAE,oBAAoB,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;wBACrD,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;wBACtB,IAAI,EAAE,YAAY;qBACnB,CAAC,CAAC;iBACJ;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,gBAAgB,EAAE;gBAChB;oBACE,IAAI,EAAE,YAAY;oBAClB,GAAG,EAAE,CAAC,CAAC;iBACR;aACF;YACD,EAAE;YACF,IAAI,EAAE;gBACJ,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE,CAAC;gBACvE,IAAI;gBACJ,WAAW,EAAE,IAAI,EAAE,WAAW,IAAI,IAAI;aACvC;YACD,UAAU;SACX;KACoC,CAAA;AACzC,CAAC;AA0FD,SAAgB,2BAA2B,CACzC,OAA4C;IAE5C,MAAM,EACJ,YAAY,EACZ,SAAS,EACT,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,QAAQ,EAC/B,gBAAgB,GAAG,UAAU,GAC9B,GAAG,OAAO,CAAA;IACX,OAAO;QACL,SAAS,EAAE;YACT,GAAG,CAAC,YAAY;gBACd,CAAC,CAAC;oBACE,gBAAgB,EAAE,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC;wBAC3C,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;4BACxB,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC;4BACtB,IAAI,EAAE,YAAY;yBACnB,CAAC,CAAC;wBACL,CAAC,CAAC;4BACE;gCACE,EAAE,EAAE,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC;gCAChC,IAAI,EAAE,YAAY;6BACnB;yBACF;iBACN;gBACH,CAAC,CAAC,EAAE,CAAC;YACP,SAAS,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;YACnC,IAAI;YACJ,gBAAgB;SACjB;KACF,CAAA;AACH,CAAC;AAiED,SAAgB,cAAc,CAC5B,OAA+B;IAE/B,MAAM,EACJ,SAAS,EACT,WAAW,EACX,eAAe,EACf,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,SAAS,EACT,gBAAgB,GAAG,UAAU,GAC9B,GAAG,OAAO,CAAA;IAEX,MAAM,iBAAiB,GAAG,oBAAoB,CAAC;QAC7C,IAAI;QACJ,IAAI;QACJ,SAAS;KACV,CAAC,CAAA;IACF,MAAM,cAAc,GAAG,iBAAiB,CAAC;QACvC,SAAS;QACT,WAAW;QACX,eAAe;QACf,MAAM;KACP,CAAC,CAAA;IACF,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,CAAA;IAEtE,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;IAC5D,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAElD,MAAM,QAAQ,GAAG;QACf,iBAAiB;QACjB,cAAc;QACd,cAAc;QACd,SAAS;QACT,wBAAwB,EAAE,gBAAgB,KAAK,UAAU;KAC1D,CAAA;IAED,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,iBAAiB,EAAE,kBAAkB,CAAC,CAAA;IAEjE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAA;AAC9B,CAAC;AAwEM,KAAK,UAAU,IAAI,CAAC,OAAqB;IAC9C,MAAM,EACJ,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,WAAW,CAAC,EAC3E,GAAG,IAAI,EACR,GAAG,OAAO,CAAA;IACX,MAAM,cAAc,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAA;IACxD,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,CAAC,MAAM,KAAK,CAC7B,cAAc,CACf,CAAiC,CAAA;QAClC,IAAI,CAAC,UAAU;YAAE,MAAM,IAAI,4BAA4B,EAAE,CAAA;QACzD,MAAM,QAAQ,GAAG,UAAU,CAAC,QAA0C,CAAA;QAEtE,MAAM,cAAc,GAAG,MAAM,CAAC,YAAY,CACxC,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,cAAc,CAAC,CAC3C,CAAA;QACD,MAAM,cAAc,GAAG,cAAc,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;QAC5D,MAAM,SAAS,GAAG,cAAc,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAElD,MAAM,SAAS,GAAG,QAAQ,CAAC,kBAAkB,CAC3C,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,CACnC,CAAA;QAED,OAAO;YACL,QAAQ,EAAE;gBACR,iBAAiB,EAAE,GAAG,CAAC,SAAS,CAC9B,IAAI,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAC3C;gBACD,cAAc;gBACd,cAAc;gBACd,SAAS;gBACT,wBAAwB,EACtB,cAAc,CAAC,SAAU,CAAC,gBAAgB,KAAK,UAAU;aAC5D;YACD,SAAS;YACT,GAAG,EAAE,UAAU;SAChB,CAAA;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,IAAI,4BAA4B,CAAC;YACrC,KAAK,EAAE,KAAc;SACtB,CAAC,CAAA;IACJ,CAAC;AACH,CAAC;AAyDD,SAAgB,MAAM,CAAC,OAAuB;IAC5C,MAAM,EAAE,SAAS,EAAE,IAAI,GAAG,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG,OAAO,CAAA;IAC1E,MAAM,EACJ,iBAAiB,EACjB,cAAc,EACd,cAAc,EACd,SAAS,EACT,wBAAwB,GACzB,GAAG,QAAQ,CAAA;IAEZ,MAAM,sBAAsB,GAAG,KAAK,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IAG/D,IAAI,sBAAsB,CAAC,MAAM,GAAG,EAAE;QAAE,OAAO,KAAK,CAAA;IAEpD,MAAM,IAAI,GAAG,sBAAsB,CAAC,EAAE,CAAE,CAAA;IAGxC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,KAAK,CAAA;IAKxC,IAAI,wBAAwB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,KAAK,CAAA;IAIpE,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,IAAI;QAAE,OAAO,KAAK,CAAA;IAGlE,MAAM,IAAI,GAAG,uBAAuB,CAAA;IACpC,IAAI,IAAI,KAAK,cAAc,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QACnE,OAAO,KAAK,CAAA;IAGd,MAAM,KAAK,GAAG,cAAc;SACzB,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;SAC7B,KAAK,CAAC,sBAAsB,CAAC,CAAA;IAChC,IAAI,CAAC,KAAK;QAAE,OAAO,KAAK,CAAA;IAGxB,MAAM,CAAC,CAAC,EAAE,mBAAmB,CAAC,GAAG,KAAK,CAAA;IACtC,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,mBAAoB,CAAC,CAAC,KAAK,SAAS;QACnE,OAAO,KAAK,CAAA;IAEd,MAAM,kBAAkB,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QACvE,EAAE,EAAE,OAAO;KACZ,CAAC,CAAA;IACF,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,sBAAsB,EAAE,kBAAkB,CAAC,CAAA;IAExE,OAAO,IAAI,CAAC,MAAM,CAAC;QACjB,IAAI;QACJ,OAAO;QACP,SAAS;QACT,SAAS;KACV,CAAC,CAAA;AACJ,CAAC;AAyBD,MAAa,6BAA8B,SAAQ,MAAM,CAAC,SAAgB;IAGxE,YAAY,EAAE,KAAK,KAAoC,EAAE;QACvD,KAAK,CAAC,8BAA8B,EAAE;YACpC,KAAK;SACN,CAAC,CAAA;QALc;;;;mBAAO,4CAA4C;WAAA;IAMrE,CAAC;CACF;AARD,sEAQC;AAGD,MAAa,4BAA6B,SAAQ,MAAM,CAAC,SAAgB;IAGvE,YAAY,EAAE,KAAK,KAAoC,EAAE;QACvD,KAAK,CAAC,+BAA+B,EAAE;YACrC,KAAK;SACN,CAAC,CAAA;QALc;;;;mBAAO,2CAA2C;WAAA;IAMpE,CAAC;CACF;AARD,oEAQC"}