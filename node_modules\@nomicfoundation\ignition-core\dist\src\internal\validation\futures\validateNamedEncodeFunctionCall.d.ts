import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { EncodeFunctionCallFuture } from "../../../types/module";
export declare function validateNamedEncodeFunctionCall(future: EncodeFunctionCallFuture<string, string>, artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateNamedEncodeFunctionCall.d.ts.map