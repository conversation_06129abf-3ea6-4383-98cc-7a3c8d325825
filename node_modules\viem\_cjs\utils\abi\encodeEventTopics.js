"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.encodeEventTopics = encodeEventTopics;
const abi_js_1 = require("../../errors/abi.js");
const log_js_1 = require("../../errors/log.js");
const toBytes_js_1 = require("../encoding/toBytes.js");
const keccak256_js_1 = require("../hash/keccak256.js");
const toEventSelector_js_1 = require("../hash/toEventSelector.js");
const encodeAbiParameters_js_1 = require("./encodeAbiParameters.js");
const formatAbiItem_js_1 = require("./formatAbiItem.js");
const getAbiItem_js_1 = require("./getAbiItem.js");
const docsPath = '/docs/contract/encodeEventTopics';
function encodeEventTopics(parameters) {
    const { abi, eventName, args } = parameters;
    let abiItem = abi[0];
    if (eventName) {
        const item = (0, getAbiItem_js_1.getAbiItem)({ abi, name: eventName });
        if (!item)
            throw new abi_js_1.AbiEventNotFoundError(eventName, { docsPath });
        abiItem = item;
    }
    if (abiItem.type !== 'event')
        throw new abi_js_1.AbiEventNotFoundError(undefined, { docsPath });
    const definition = (0, formatAbiItem_js_1.formatAbiItem)(abiItem);
    const signature = (0, toEventSelector_js_1.toEventSelector)(definition);
    let topics = [];
    if (args && 'inputs' in abiItem) {
        const indexedInputs = abiItem.inputs?.filter((param) => 'indexed' in param && param.indexed);
        const args_ = Array.isArray(args)
            ? args
            : Object.values(args).length > 0
                ? (indexedInputs?.map((x) => args[x.name]) ?? [])
                : [];
        if (args_.length > 0) {
            topics =
                indexedInputs?.map((param, i) => {
                    if (Array.isArray(args_[i]))
                        return args_[i].map((_, j) => encodeArg({ param, value: args_[i][j] }));
                    return typeof args_[i] !== 'undefined' && args_[i] !== null
                        ? encodeArg({ param, value: args_[i] })
                        : null;
                }) ?? [];
        }
    }
    return [signature, ...topics];
}
function encodeArg({ param, value, }) {
    if (param.type === 'string' || param.type === 'bytes')
        return (0, keccak256_js_1.keccak256)((0, toBytes_js_1.toBytes)(value));
    if (param.type === 'tuple' || param.type.match(/^(.*)\[(\d+)?\]$/))
        throw new log_js_1.FilterTypeNotSupportedError(param.type);
    return (0, encodeAbiParameters_js_1.encodeAbiParameters)([param], [value]);
}
//# sourceMappingURL=encodeEventTopics.js.map