import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const etherlink = /*#__PURE__*/ defineChain({
    id: 42793,
    name: 'Etherlink',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'XTZ',
    },
    rpcUrls: {
        default: { http: ['https://node.mainnet.etherlink.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Etherlink',
            url: 'https://explorer.etherlink.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 33899,
        },
    },
});
//# sourceMappingURL=etherlink.js.map