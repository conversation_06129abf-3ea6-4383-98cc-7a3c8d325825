import type * as Address from './Address.js'
import type * as Errors from './Errors.js'
import * as Hash from './Hash.js'
import * as Hex from './Hex.js'
import * as Rlp from './Rlp.js'
import * as Signature from './Signature.js'
import type { Compute, Mutable, Undefined } from './internal/types.js'

/** Root type for an EIP-7702 Authorization. */
export type Authorization<
  signed extends boolean = boolean,
  bigintType = bigint,
  numberType = number,
> = Compute<
  {
    /** Address of the contract to set as code for the Authority. */
    address: Address.Address
    /** Chain ID to authorize. */
    chainId: numberType
    /** Nonce of the Authority to authorize. */
    nonce: bigintType
  } & (signed extends true
    ? Signature.Signature<true, bigintType, numberType>
    : Undefined<Signature.Signature>)
>

/** RPC representation of an {@link ox#Authorization.Authorization}. */
export type Rpc = Authorization<true, Hex.Hex, Hex.Hex>

/** List of {@link ox#Authorization.Authorization}. */
export type List<
  signed extends boolean = boolean,
  bigintType = bigint,
  numberType = number,
> = Compute<readonly Authorization<signed, bigintType, numberType>[]>

/** RPC representation of an {@link ox#Authorization.List}. */
export type ListRpc = List<true, Hex.Hex, Hex.Hex>

/** Signed representation of a list of {@link ox#Authorization.Authorization}. */
export type ListSigned<bigintType = bigint, numberType = number> = List<
  true,
  bigintType,
  numberType
>

/** Signed representation of an {@link ox#Authorization.Authorization}. */
export type Signed<bigintType = bigint, numberType = number> = Authorization<
  true,
  bigintType,
  numberType
>

/** Tuple representation of an {@link ox#Authorization.Authorization}. */
export type Tuple<signed extends boolean = boolean> = signed extends true
  ? readonly [
      chainId: Hex.Hex,
      address: Hex.Hex,
      nonce: Hex.Hex,
      yParity: Hex.Hex,
      r: Hex.Hex,
      s: Hex.Hex,
    ]
  : readonly [chainId: Hex.Hex, address: Hex.Hex, nonce: Hex.Hex]

/** Tuple representation of a signed {@link ox#Authorization.Authorization}. */
export type TupleSigned = Tuple<true>

/** Tuple representation of a list of {@link ox#Authorization.Authorization}. */
export type TupleList<signed extends boolean = boolean> =
  readonly Tuple<signed>[]

/** Tuple representation of a list of signed {@link ox#Authorization.Authorization}. */
export type TupleListSigned = TupleList<true>

/**
 * Converts an [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization object into a typed {@link ox#Authorization.Authorization}.
 *
 * @example
 * An Authorization can be instantiated from an [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple in object format.
 *
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 * ```
 *
 * @example
 * ### Attaching Signatures
 *
 * A {@link ox#Signature.Signature} can be attached with the `signature` option. The example below demonstrates signing
 * an Authorization with {@link ox#Secp256k1.(sign:function)}.
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1 } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 40n,
 * })
 *
 * const signature = Secp256k1.sign({
 *   payload: Authorization.getSignPayload(authorization),
 *   privateKey: '0x...',
 * })
 *
 * const authorization_signed = Authorization.from(authorization, { signature }) // [!code focus]
 * ```
 *
 * @param authorization - An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple in object format.
 * @param options - Authorization options.
 * @returns The {@link ox#Authorization.Authorization}.
 */
export function from<
  const authorization extends Authorization | Rpc,
  const signature extends Signature.Signature | undefined = undefined,
>(
  authorization: authorization | Authorization,
  options: from.Options<signature> = {},
): from.ReturnType<authorization, signature> {
  if (typeof authorization.chainId === 'string')
    return fromRpc(authorization) as never
  return { ...authorization, ...options.signature } as never
}

export declare namespace from {
  type Options<
    signature extends Signature.Signature | undefined =
      | Signature.Signature
      | undefined,
  > = {
    /** The {@link ox#Signature.Signature} to attach to the Authorization. */
    signature?: signature | Signature.Signature | undefined
  }

  type ReturnType<
    authorization extends Authorization | Rpc = Authorization,
    signature extends Signature.Signature | undefined =
      | Signature.Signature
      | undefined,
  > = Compute<
    authorization extends Rpc
      ? Signed
      : authorization &
          (signature extends Signature.Signature ? Readonly<signature> : {})
  >

  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.Rpc} to an {@link ox#Authorization.Authorization}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.fromRpc({
 *   address: '0x0000000000000000000000000000000000000000',
 *   chainId: '0x1',
 *   nonce: '0x1',
 *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',
 *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',
 *   yParity: '0x0',
 * })
 * ```
 *
 * @param authorization - The RPC-formatted Authorization.
 * @returns A signed {@link ox#Authorization.Authorization}.
 */
export function fromRpc(authorization: Rpc): Signed {
  const { address, chainId, nonce } = authorization
  const signature = Signature.extract(authorization)!

  return {
    address,
    chainId: Number(chainId),
    nonce: BigInt(nonce),
    ...signature,
  }
}

export declare namespace fromRpc {
  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.ListRpc} to an {@link ox#Authorization.List}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorizationList = Authorization.fromRpcList([{
 *   address: '0x0000000000000000000000000000000000000000',
 *   chainId: '0x1',
 *   nonce: '0x1',
 *   r: '0x635dc2033e60185bb36709c29c75d64ea51dfbd91c32ef4be198e4ceb169fb4d',
 *   s: '0x50c2667ac4c771072746acfdcf1f1483336dcca8bd2df47cd83175dbe60f0540',
 *   yParity: '0x0',
 * }])
 * ```
 *
 * @param authorizationList - The RPC-formatted Authorization list.
 * @returns A signed {@link ox#Authorization.List}.
 */
export function fromRpcList(authorizationList: ListRpc): ListSigned {
  return authorizationList.map(fromRpc)
}

export declare namespace fromRpcList {
  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.Tuple} to an {@link ox#Authorization.Authorization}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.fromTuple([
 *   '0x1',
 *   '******************************************',
 *   '0x3'
 * ])
 * // @log: {
 * // @log:   address: '******************************************',
 * // @log:   chainId: 1,
 * // @log:   nonce: 3n
 * // @log: }
 * ```
 *
 * @example
 * It is also possible to append a Signature tuple to the end of an Authorization tuple.
 *
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.fromTuple([
 *   '0x1',
 *   '******************************************',
 *   '0x3',
 *   '0x1',
 *   '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90',
 *   '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064',
 * ])
 * // @log: {
 * // @log:   address: '******************************************',
 * // @log:   chainId: 1,
 * // @log:   nonce: 3n
 * // @log:   r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),
 * // @log:   s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),
 * // @log:   yParity: 0,
 * // @log: }
 * ```
 *
 * @param tuple - The [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple.
 * @returns The {@link ox#Authorization.Authorization}.
 */
export function fromTuple<const tuple extends Tuple>(
  tuple: tuple,
): fromTuple.ReturnType<tuple> {
  const [chainId, address, nonce, yParity, r, s] = tuple
  const signature =
    yParity && r && s ? Signature.fromTuple([yParity, r, s]) : undefined
  return from({
    address,
    chainId: Number(chainId),
    nonce: BigInt(nonce),
    ...signature,
  }) as never
}

export declare namespace fromTuple {
  type ReturnType<authorization extends Tuple = Tuple> = Compute<
    Authorization<authorization extends Tuple<true> ? true : false>
  >

  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.TupleList} to an {@link ox#Authorization.List}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorizationList = Authorization.fromTupleList([
 *   ['0x1', '******************************************', '0x3'],
 *   ['0x3', '******************************************', '0x14'],
 * ])
 * // @log: [
 * // @log:   {
 * // @log:     address: '******************************************',
 * // @log:     chainId: 1,
 * // @log:     nonce: 3n,
 * // @log:   },
 * // @log:   {
 * // @log:     address: '******************************************',
 * // @log:     chainId: 3,
 * // @log:     nonce: 20n,
 * // @log:   },
 * // @log: ]
 * ```
 *
 * @example
 * It is also possible to append a Signature tuple to the end of an Authorization tuple.
 *
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorizationList = Authorization.fromTupleList([
 *   ['0x1', '******************************************', '0x3', '0x1', '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90', '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'],
 *   ['0x3', '******************************************', '0x14', '0x1', '0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90', '0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'],
 * ])
 * // @log: [
 * // @log:   {
 * // @log:     address: '******************************************',
 * // @log:     chainId: 1,
 * // @log:     nonce: 3n,
 * // @log:     r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),
 * // @log:     s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),
 * // @log:     yParity: 0,
 * // @log:   },
 * // @log:   {
 * // @log:     address: '******************************************',
 * // @log:     chainId: 3,
 * // @log:     nonce: 20n,
 * // @log:     r: BigInt('0x68a020a209d3d56c46f38cc50a33f704f4a9a10a59377f8dd762ac66910e9b90'),
 * // @log:     s: BigInt('0x7e865ad05c4035ab5792787d4a0297a43617ae897930a6fe4d822b8faea52064'),
 * // @log:     yParity: 0,
 * // @log:   },
 * // @log: ]
 * ```
 *
 * @param tupleList - The [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple list.
 * @returns An {@link ox#Authorization.List}.
 */
export function fromTupleList<const tupleList extends TupleList>(
  tupleList: tupleList,
): fromTupleList.ReturnType<tupleList> {
  const list: Mutable<List> = []
  for (const tuple of tupleList) list.push(fromTuple(tuple))
  return list as never
}

export declare namespace fromTupleList {
  type ReturnType<tupleList extends TupleList> = Compute<
    TupleList<tupleList extends TupleList<true> ? true : false>
  >

  type ErrorType = Errors.GlobalErrorType
}

/**
 * Computes the sign payload for an {@link ox#Authorization.Authorization} in [EIP-7702 format](https://eips.ethereum.org/EIPS/eip-7702): `keccak256('0x05' || rlp([chain_id, address, nonce]))`.
 *
 * @example
 * The example below demonstrates computing the sign payload for an {@link ox#Authorization.Authorization}. This payload
 * can then be passed to signing functions like {@link ox#Secp256k1.(sign:function)}.
 *
 * ```ts twoslash
 * import { Authorization, Secp256k1 } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 *
 * const payload = Authorization.getSignPayload(authorization) // [!code focus]
 *
 * const signature = Secp256k1.sign({
 *   payload,
 *   privateKey: '0x...',
 * })
 * ```
 *
 * @param authorization - The {@link ox#Authorization.Authorization}.
 * @returns The sign payload.
 */
export function getSignPayload(authorization: Authorization): Hex.Hex {
  return hash(authorization)
}

export declare namespace getSignPayload {
  type ErrorType = hash.ErrorType | Errors.GlobalErrorType
}

/**
 * Computes the hash for an {@link ox#Authorization.Authorization} in [EIP-7702 format](https://eips.ethereum.org/EIPS/eip-7702): `keccak256('0x05' || rlp([chain_id, address, nonce]))`.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 *
 * const hash = Authorization.hash(authorization) // [!code focus]
 * ```
 *
 * @param authorization - The {@link ox#Authorization.Authorization}.
 * @returns The hash.
 */
export function hash(authorization: Authorization): Hex.Hex {
  return Hash.keccak256(Hex.concat('0x05', Rlp.fromHex(toTuple(authorization))))
}

export declare namespace hash {
  type ErrorType =
    | toTuple.ErrorType
    | Hash.keccak256.ErrorType
    | Hex.concat.ErrorType
    | Rlp.fromHex.ErrorType
    | Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.Authorization} to an {@link ox#Authorization.Rpc}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.toRpc({
 *   address: '0x0000000000000000000000000000000000000000',
 *   chainId: 1,
 *   nonce: 1n,
 *   r: 44944627813007772897391531230081695102703289123332187696115181104739239197517n,
 *   s: 36528503505192438307355164441104001310566505351980369085208178712678799181120n,
 *   yParity: 0,
 * })
 * ```
 *
 * @param authorization - An Authorization.
 * @returns An RPC-formatted Authorization.
 */
export function toRpc(authorization: Signed): Rpc {
  const { address, chainId, nonce, ...signature } = authorization

  return {
    address,
    chainId: Hex.fromNumber(chainId),
    nonce: Hex.fromNumber(nonce),
    ...Signature.toRpc(signature),
  }
}

export declare namespace toRpc {
  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.List} to an {@link ox#Authorization.ListRpc}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.toRpcList([{
 *   address: '0x0000000000000000000000000000000000000000',
 *   chainId: 1,
 *   nonce: 1n,
 *   r: 44944627813007772897391531230081695102703289123332187696115181104739239197517n,
 *   s: 36528503505192438307355164441104001310566505351980369085208178712678799181120n,
 *   yParity: 0,
 * }])
 * ```
 *
 * @param authorizationList - An Authorization List.
 * @returns An RPC-formatted Authorization List.
 */
export function toRpcList(authorizationList: ListSigned): ListRpc {
  return authorizationList.map(toRpc)
}

export declare namespace toRpcList {
  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.Authorization} to an {@link ox#Authorization.Tuple}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 *
 * const tuple = Authorization.toTuple(authorization) // [!code focus]
 * // @log: [
 * // @log:   address: '******************************************',
 * // @log:   chainId: 1,
 * // @log:   nonce: 69n,
 * // @log: ]
 * ```
 *
 * @param authorization - The {@link ox#Authorization.Authorization}.
 * @returns An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple.
 */
export function toTuple<const authorization extends Authorization>(
  authorization: authorization,
): toTuple.ReturnType<authorization> {
  const { address, chainId, nonce } = authorization
  const signature = Signature.extract(authorization)
  return [
    chainId ? Hex.fromNumber(chainId) : '0x',
    address,
    nonce ? Hex.fromNumber(nonce) : '0x',
    ...(signature ? Signature.toTuple(signature) : []),
  ] as never
}

export declare namespace toTuple {
  type ReturnType<authorization extends Authorization = Authorization> =
    Compute<Tuple<authorization extends Signature.Signature ? true : false>>

  type ErrorType = Errors.GlobalErrorType
}

/**
 * Converts an {@link ox#Authorization.List} to an {@link ox#Authorization.TupleList}.
 *
 * @example
 * ```ts twoslash
 * import { Authorization } from 'ox'
 *
 * const authorization_1 = Authorization.from({
 *   address: '******************************************',
 *   chainId: 1,
 *   nonce: 69n,
 * })
 * const authorization_2 = Authorization.from({
 *   address: '******************************************',
 *   chainId: 3,
 *   nonce: 20n,
 * })
 *
 * const tuple = Authorization.toTupleList([authorization_1, authorization_2]) // [!code focus]
 * // @log: [
 * // @log:   [
 * // @log:     address: '******************************************',
 * // @log:     chainId: 1,
 * // @log:     nonce: 69n,
 * // @log:   ],
 * // @log:   [
 * // @log:     address: '******************************************',
 * // @log:     chainId: 3,
 * // @log:     nonce: 20n,
 * // @log:   ],
 * // @log: ]
 * ```
 *
 * @param list - An {@link ox#Authorization.List}.
 * @returns An [EIP-7702](https://eips.ethereum.org/EIPS/eip-7702) Authorization tuple list.
 */
export function toTupleList<
  const list extends
    | readonly Authorization<true>[]
    | readonly Authorization<false>[],
>(list?: list | undefined): toTupleList.ReturnType<list> {
  if (!list || list.length === 0) return []

  const tupleList: Mutable<TupleList> = []
  for (const authorization of list) tupleList.push(toTuple(authorization))

  return tupleList as never
}

export declare namespace toTupleList {
  type ReturnType<
    list extends
      | readonly Authorization<true>[]
      | readonly Authorization<false>[],
  > = Compute<
    TupleList<list extends readonly Authorization<true>[] ? true : false>
  >

  type ErrorType = Errors.GlobalErrorType
}
