{"version": 3, "file": "WrappedSignature.d.ts", "sourceRoot": "", "sources": ["../../erc6492/WrappedSignature.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,aAAa,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,KAAK,OAAO,MAAM,oBAAoB,CAAA;AAClD,OAAO,KAAK,MAAM,MAAM,mBAAmB,CAAA;AAC3C,OAAO,KAAK,GAAG,MAAM,gBAAgB,CAAA;AACrC,OAAO,KAAK,SAAS,MAAM,sBAAsB,CAAA;AAEjD,kCAAkC;AAClC,MAAM,MAAM,gBAAgB,GAAG;IAC7B,8EAA8E;IAC9E,IAAI,EAAE,GAAG,CAAC,GAAG,CAAA;IACb,8BAA8B;IAC9B,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;IAC9B,iEAAiE;IACjE,EAAE,EAAE,OAAO,CAAC,OAAO,CAAA;CACpB,CAAA;AAED;;GAEG;AACH,eAAO,MAAM,UAAU,sEACwD,CAAA;AAE/E;;GAEG;AACH,eAAO,MAAM,mCAAmC,+yGAC8vG,CAAA;AAE9yG;;;;GAIG;AACH,eAAO,MAAM,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2Cf,CAAA;AAE5B;;;;;;;;;;;;GAYG;AACH,wBAAgB,MAAM,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,QAGtC;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,SAAS,GACV,4BAA4B,GAC5B,GAAG,CAAC,KAAK,CAAC,SAAS,GACnB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,wBAAgB,IAAI,CAAC,OAAO,EAAE,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG,gBAAgB,CAG1E;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,UAAU,GAAG,gBAAgB,CAAA;IAElC,KAAK,SAAS,GACV,aAAa,CAAC,IAAI,CAAC,SAAS,GAC5B,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,SAAS,CAAC,OAAO,CAAC,SAAS,GAC3B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;GAYG;AACH,wBAAgB,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,gBAAgB,CAW1D;AAED,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,KAAK,SAAS,GACV,aAAa,CAAC,IAAI,CAAC,SAAS,GAC5B,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,SAAS,CAAC,OAAO,CAAC,SAAS,GAC3B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,KAAK,CAAC,KAAK,EAAE,gBAAgB,GAAG,GAAG,CAAC,GAAG,CAWtD;AAED,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,KAAK,SAAS,GACV,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,SAAS,CAAC,KAAK,CAAC,SAAS,GACzB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;GAaG;AACH,wBAAgB,QAAQ,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,OAAO,CAOlD;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED,6DAA6D;AAC7D,qBAAa,4BAA6B,SAAQ,MAAM,CAAC,SAAS;IAChE,SAAkB,IAAI,mDAAkD;gBAE5D,OAAO,EAAE,GAAG,CAAC,GAAG;CAG7B"}