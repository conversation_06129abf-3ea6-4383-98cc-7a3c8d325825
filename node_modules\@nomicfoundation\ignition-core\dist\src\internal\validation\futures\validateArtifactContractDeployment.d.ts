import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { ContractDeploymentFuture } from "../../../types/module";
export declare function validateArtifactContractDeployment(future: ContractDeploymentFuture, _artifactLoader: ArtifactResolver, deploymentParameters: DeploymentParameters, accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateArtifactContractDeployment.d.ts.map