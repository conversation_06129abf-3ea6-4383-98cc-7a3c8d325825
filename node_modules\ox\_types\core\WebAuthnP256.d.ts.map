{"version": 3, "file": "WebAuthnP256.d.ts", "sourceRoot": "", "sources": ["../../core/WebAuthnP256.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,KAAK,MAAM,YAAY,CAAA;AACnC,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAChD,OAAO,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,qBAAqB,CAAA;AACzD,OAAO,KAAK,QAAQ,MAAM,wBAAwB,CAAA;AAElD,2CAA2C;AAC3C,MAAM,MAAM,cAAc,GAAG;IAC3B,EAAE,EAAE,MAAM,CAAA;IACV,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;IAC9B,GAAG,EAAE,QAAQ,CAAC,mBAAmB,CAAA;CAClC,CAAA;AAED,8CAA8C;AAC9C,MAAM,MAAM,YAAY,GAAG,OAAO,CAAC;IACjC,iBAAiB,EAAE,GAAG,CAAC,GAAG,CAAA;IAC1B,cAAc,EAAE,MAAM,CAAA;IACtB,cAAc,EAAE,MAAM,CAAA;IACtB,SAAS,EAAE,MAAM,CAAA;IACjB,wBAAwB,EAAE,OAAO,CAAA;CAClC,CAAC,CAAA;AAEF,eAAO,MAAM,eAAe,YAE1B,CAAA;AAEF;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAsB,gBAAgB,CACpC,OAAO,EAAE,gBAAgB,CAAC,OAAO,GAChC,OAAO,CAAC,cAAc,CAAC,CA2BzB;AAED,MAAM,CAAC,OAAO,WAAW,gBAAgB,CAAC;IACxC,KAAK,OAAO,GAAG,4BAA4B,CAAC,OAAO,GAAG;QACpD;;;;;WAKG;QACH,QAAQ,CAAC,EACL,CAAC,CACC,OAAO,CAAC,EAAE,QAAQ,CAAC,yBAAyB,GAAG,SAAS,KACrD,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,GACzC,SAAS,CAAA;KACd,CAAA;IAED,KAAK,SAAS,GACV,4BAA4B,CAAC,SAAS,GACtC,QAAQ,CAAC,wBAAwB,CAAC,SAAS,GAC3C,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,oBAAoB,CAClC,OAAO,GAAE,oBAAoB,CAAC,OAAY,GACzC,GAAG,CAAC,GAAG,CAMT;AAED,MAAM,CAAC,OAAO,WAAW,oBAAoB,CAAC;IAC5C,KAAK,OAAO,GAAG;QACb,0MAA0M;QAC1M,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzB,yHAAyH;QACzH,IAAI,CAAC,EAAE,QAAQ,CAAC,iCAAiC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;QACrE,mFAAmF;QACnF,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC/B,CAAA;IAED,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,iBAAiB,CAAC,OAAO,EAAE,iBAAiB,CAAC,OAAO,GAAG,MAAM,CAe5E;AAED,MAAM,CAAC,OAAO,WAAW,iBAAiB,CAAC;IACzC,KAAK,OAAO,GAAG;QACb,6BAA6B;QAC7B,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;QAClB,6HAA6H;QAC7H,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACjC,iEAAiE;QACjE,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,CAAA;QACrD,uHAAuH;QACvH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC5B,CAAA;IAED,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;GAeG;AACH,wBAAgB,4BAA4B,CAC1C,OAAO,EAAE,4BAA4B,CAAC,OAAO,GAC5C,QAAQ,CAAC,yBAAyB,CA+CpC;AAED,MAAM,CAAC,OAAO,WAAW,4BAA4B,CAAC;IACpD,KAAK,OAAO,GAAG;QACb;;;;WAIG;QACH,WAAW,CAAC,EACR,QAAQ,CAAC,kCAAkC,CAAC,aAAa,CAAC,GAC1D,SAAS,CAAA;QACb;;;WAGG;QACH,sBAAsB,CAAC,EACnB,QAAQ,CAAC,kCAAkC,CAAC,wBAAwB,CAAC,GACrE,SAAS,CAAA;QACb;;WAEG;QACH,SAAS,CAAC,EACN,QAAQ,CAAC,kCAAkC,CAAC,WAAW,CAAC,GACxD,SAAS,CAAA;QACb;;;WAGG;QACH,oBAAoB,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,SAAS,CAAA;QACpD;;WAEG;QACH,UAAU,CAAC,EACP,QAAQ,CAAC,kCAAkC,CAAC,YAAY,CAAC,GACzD,SAAS,CAAA;QACb;;WAEG;QACH,EAAE,CAAC,EACC;YACE,EAAE,EAAE,MAAM,CAAA;YACV,IAAI,EAAE,MAAM,CAAA;SACb,GACD,SAAS,CAAA;QACb;;WAEG;QACH,OAAO,CAAC,EAAE,QAAQ,CAAC,kCAAkC,CAAC,SAAS,CAAC,GAAG,SAAS,CAAA;KAC7E,GAAG,KAAK,CACL;QACE,2CAA2C;QAC3C,IAAI,EAAE,MAAM,CAAA;KACb,GACD;QACE;;WAEG;QACH,IAAI,EAAE;YACJ,WAAW,CAAC,EAAE,MAAM,CAAA;YACpB,EAAE,CAAC,EAAE,YAAY,CAAA;YACjB,IAAI,EAAE,MAAM,CAAA;SACb,CAAA;KACF,CACJ,CAAA;IAED,KAAK,SAAS,GACV,MAAM,CAAC,OAAO,CAAC,SAAS,GACxB,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,KAAK,CAAC,UAAU,CAAC,SAAS,GAC1B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,wBAAgB,2BAA2B,CACzC,OAAO,EAAE,2BAA2B,CAAC,OAAO,GAC3C,QAAQ,CAAC,wBAAwB,CA6BnC;AAED,MAAM,CAAC,OAAO,WAAW,2BAA2B,CAAC;IACnD,KAAK,OAAO,GAAG;QACb,gCAAgC;QAChC,YAAY,CAAC,EAAE,MAAM,GAAG,MAAM,EAAE,GAAG,SAAS,CAAA;QAC5C,6BAA6B;QAC7B,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;QAClB,2CAA2C;QAC3C,IAAI,CAAC,EAAE,QAAQ,CAAC,iCAAiC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;QACrE,yCAAyC;QACzC,gBAAgB,CAAC,EACb,QAAQ,CAAC,iCAAiC,CAAC,kBAAkB,CAAC,GAC9D,SAAS,CAAA;KACd,CAAA;IAED,KAAK,SAAS,GACV,KAAK,CAAC,OAAO,CAAC,SAAS,GACvB,MAAM,CAAC,OAAO,CAAC,SAAS,GACxB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA0CG;AACH,wBAAgB,cAAc,CAC5B,OAAO,EAAE,cAAc,CAAC,OAAO,GAC9B,cAAc,CAAC,UAAU,CAuC3B;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,OAAO,GAAG;QACb,6BAA6B;QAC7B,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;QAClB,6HAA6H;QAC7H,WAAW,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QACjC,iEAAiE;QACjE,eAAe,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG,SAAS,CAAA;QACrD,0EAA0E;QAC1E,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC1B,0MAA0M;QAC1M,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzB,uHAAuH;QACvH,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC3B,yHAAyH;QACzH,IAAI,CAAC,EAAE,QAAQ,CAAC,iCAAiC,CAAC,MAAM,CAAC,GAAG,SAAS,CAAA;QACrE,mFAAmF;QACnF,SAAS,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QAC9B,6EAA6E;QAC7E,gBAAgB,CAAC,EACb,QAAQ,CAAC,iCAAiC,CAAC,kBAAkB,CAAC,GAC9D,SAAS,CAAA;KACd,CAAA;IAED,KAAK,UAAU,GAAG;QAChB,QAAQ,EAAE,YAAY,CAAA;QACtB,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;KACjB,CAAA;IAED,KAAK,SAAS,GACV,IAAI,CAAC,MAAM,CAAC,SAAS,GACrB,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,oBAAoB,CAAC,SAAS,GAC9B,iBAAiB,CAAC,SAAS,GAC3B,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA+BG;AACH,wBAAsB,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CA0C1E;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,GAAG,2BAA2B,CAAC,OAAO,GAAG;QACnD;;;;;WAKG;QACH,KAAK,CAAC,EACF,CAAC,CACC,OAAO,CAAC,EAAE,QAAQ,CAAC,wBAAwB,GAAG,SAAS,KACpD,OAAO,CAAC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC,CAAC,GACzC,SAAS,CAAA;KACd,CAAA;IAED,KAAK,UAAU,GAAG;QAChB,QAAQ,EAAE,YAAY,CAAA;QACtB,GAAG,EAAE,QAAQ,CAAC,mBAAmB,CAAA;QACjC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;KACtC,CAAA;IAED,KAAK,SAAS,GACV,GAAG,CAAC,SAAS,CAAC,SAAS,GACvB,2BAA2B,CAAC,SAAS,GACrC,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,OAAO,CAwDvD;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,OAAO,GAAG;QACb,+BAA+B;QAC/B,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;QAClB,mFAAmF;QACnF,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC1B,mDAAmD;QACnD,SAAS,EAAE,SAAS,CAAC,SAAS,CAAA;QAC9B,+BAA+B;QAC/B,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QACrC,iDAAiD;QACjD,QAAQ,EAAE,YAAY,CAAA;KACvB,CAAA;IAED,KAAK,SAAS,GACV,MAAM,CAAC,OAAO,CAAC,SAAS,GACxB,KAAK,CAAC,MAAM,CAAC,SAAS,GACtB,KAAK,CAAC,OAAO,CAAC,SAAS,GACvB,IAAI,CAAC,MAAM,CAAC,SAAS,GACrB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED,6DAA6D;AAC7D,qBAAa,6BAA8B,SAAQ,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IACxE,SAAkB,IAAI,gDAA+C;gBAEzD,EAAE,KAAK,EAAE,GAAE;QAAE,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;KAAO;CAK1D;AAED,4DAA4D;AAC5D,qBAAa,4BAA6B,SAAQ,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC;IACvE,SAAkB,IAAI,+CAA8C;gBAExD,EAAE,KAAK,EAAE,GAAE;QAAE,KAAK,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;KAAO;CAK1D"}