import { ArtifactResolver } from "../../../types/artifact";
import { DeploymentParameters } from "../../../types/deploy";
import { ReadEventArgumentFuture } from "../../../types/module";
export declare function validateReadEventArgument(future: ReadEventArgumentFuture, artifactLoader: ArtifactResolver, _deploymentParameters: DeploymentParameters, _accounts: string[]): Promise<string[]>;
//# sourceMappingURL=validateReadEventArgument.d.ts.map