{"version": 3, "file": "UserOperation.d.ts", "sourceRoot": "", "sources": ["../../erc4337/UserOperation.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,aAAa,MAAM,0BAA0B,CAAA;AACzD,OAAO,KAAK,KAAK,OAAO,MAAM,oBAAoB,CAAA;AAClD,OAAO,KAAK,KAAK,MAAM,MAAM,mBAAmB,CAAA;AAChD,OAAO,KAAK,IAAI,MAAM,iBAAiB,CAAA;AACvC,OAAO,KAAK,GAAG,MAAM,gBAAgB,CAAA;AACrC,OAAO,KAAK,SAAS,MAAM,sBAAsB,CAAA;AACjD,OAAO,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,2BAA2B,CAAA;AACvE,OAAO,KAAK,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAElD,sBAAsB;AACtB,MAAM,MAAM,aAAa,CACvB,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EACjE,MAAM,SAAS,OAAO,GAAG,OAAO,EAChC,UAAU,GAAG,MAAM,IACjB,KAAK,CACL,CAAC,iBAAiB,SAAS,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,GACnE,CAAC,iBAAiB,SAAS,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,KAAK,CAAC,CACtE,CAAA;AAED;;;;GAIG;AACH,MAAM,MAAM,MAAM,GAAG;IACnB,uFAAuF;IACvF,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAA;IACzB,uEAAuE;IACvE,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAA;IACjB,oDAAoD;IACpD,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAA;IACjB,iFAAiF;IACjF,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;IAChB,6BAA6B;IAC7B,KAAK,EAAE,MAAM,CAAA;IACb,oDAAoD;IACpD,gBAAgB,EAAE,GAAG,CAAC,GAAG,CAAA;IACzB,oCAAoC;IACpC,kBAAkB,EAAE,MAAM,CAAA;IAC1B,wCAAwC;IACxC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAA;IACvB,4DAA4D;IAC5D,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;CACnB,CAAA;AAED,+BAA+B;AAC/B,MAAM,MAAM,GAAG,CACb,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EACjE,MAAM,SAAS,OAAO,GAAG,IAAI,IAC3B,KAAK,CACL,CAAC,iBAAiB,SAAS,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAChE,CAAC,iBAAiB,SAAS,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,CACnE,CAAA;AAED,wBAAwB;AACxB,MAAM,MAAM,eAAe,CACzB,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EACjE,UAAU,GAAG,MAAM,IACjB;IACF,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;IAClB,WAAW,EAAE,UAAU,CAAA;IACvB,UAAU,EAAE,OAAO,CAAC,OAAO,CAAA;IAC3B,eAAe,EAAE,GAAG,CAAC,GAAG,CAAA;IACxB,aAAa,EAAE,aAAa,CAAC,iBAAiB,EAAE,IAAI,EAAE,UAAU,CAAC,CAAA;CAClE,CAAA;AAED,4BAA4B;AAC5B,MAAM,MAAM,kBAAkB,CAC5B,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAC/D,eAAe,CAAC,iBAAiB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;AAE/C,gDAAgD;AAChD,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,OAAO,GAAG,OAAO,EAAE,UAAU,GAAG,MAAM,IAAI;IACvE,uEAAuE;IACvE,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAA;IACjB,4DAA4D;IAC5D,YAAY,EAAE,UAAU,CAAA;IACxB,gDAAgD;IAChD,QAAQ,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IAC9B,2BAA2B;IAC3B,YAAY,EAAE,UAAU,CAAA;IACxB,oCAAoC;IACpC,oBAAoB,EAAE,UAAU,CAAA;IAChC,6BAA6B;IAC7B,KAAK,EAAE,UAAU,CAAA;IACjB,uCAAuC;IACvC,gBAAgB,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IACtC,oCAAoC;IACpC,kBAAkB,EAAE,UAAU,CAAA;IAC9B,wCAAwC;IACxC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAA;IACvB,4DAA4D;IAC5D,SAAS,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IAC/B,+DAA+D;IAC/D,oBAAoB,EAAE,UAAU,CAAA;CACjC,GAAG,CAAC,MAAM,SAAS,IAAI,GAAG;IAAE,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;CAAE,GAAG,EAAE,CAAC,CAAA;AAEvD,2CAA2C;AAC3C,MAAM,MAAM,MAAM,CAAC,MAAM,SAAS,OAAO,GAAG,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;AAExE,gDAAgD;AAChD,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,OAAO,GAAG,OAAO,EAAE,UAAU,GAAG,MAAM,IAAI;IACvE,uEAAuE;IACvE,QAAQ,EAAE,GAAG,CAAC,GAAG,CAAA;IACjB,4DAA4D;IAC5D,YAAY,EAAE,UAAU,CAAA;IACxB,8CAA8C;IAC9C,OAAO,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;IACrC,gCAAgC;IAChC,WAAW,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IACjC,2BAA2B;IAC3B,YAAY,EAAE,UAAU,CAAA;IACxB,oCAAoC;IACpC,oBAAoB,EAAE,UAAU,CAAA;IAChC,6BAA6B;IAC7B,KAAK,EAAE,UAAU,CAAA;IACjB,qCAAqC;IACrC,SAAS,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;IACvC,0BAA0B;IAC1B,aAAa,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IACnC,2EAA2E;IAC3E,uBAAuB,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAChD,uEAAuE;IACvE,6BAA6B,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACtD,oCAAoC;IACpC,kBAAkB,EAAE,UAAU,CAAA;IAC9B,wCAAwC;IACxC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAA;IACvB,4DAA4D;IAC5D,SAAS,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IAC/B,+DAA+D;IAC/D,oBAAoB,EAAE,UAAU,CAAA;CACjC,GAAG,CAAC,MAAM,SAAS,IAAI,GAAG;IAAE,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;CAAE,GAAG,EAAE,CAAC,CAAA;AAEvD,2CAA2C;AAC3C,MAAM,MAAM,MAAM,CAAC,MAAM,SAAS,OAAO,GAAG,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;AAExE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAmDG;AACH,wBAAgB,IAAI,CAClB,KAAK,CAAC,aAAa,SAAS,aAAa,EACzC,KAAK,CAAC,SAAS,SAAS,GAAG,CAAC,GAAG,GAAG,SAAS,GAAG,SAAS,EAEvD,aAAa,EAAE,aAAa,GAAG,aAAa,EAC5C,OAAO,GAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAM,GACpC,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,SAAS,CAAC,CAO3C;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAY,OAAO,CACjB,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,GAAG,SAAS,IACrE;QACF,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;KAClE,CAAA;IAED,KAAY,UAAU,CACpB,aAAa,SAAS,aAAa,GAAG,aAAa,EACnD,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAAG,SAAS,GAAG,SAAS,IACrE,OAAO,CACT,MAAM,CACJ,aAAa,EACb,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,GAC3C,QAAQ,CAAC;QAAE,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;KAAE,CAAC,GAChC,EAAE,CACP,CACF,CAAA;IAED,KAAY,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/C;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,OAAO,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAgB/C;AAED,MAAM,CAAC,OAAO,WAAW,OAAO,CAAC;IAC/B,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA8BG;AACH,wBAAgB,cAAc,CAC5B,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EAEjE,aAAa,EAAE,aAAa,CAAC,iBAAiB,CAAC,EAC/C,OAAO,EAAE,cAAc,CAAC,OAAO,CAAC,iBAAiB,CAAC,GACjD,GAAG,CAAC,GAAG,CAET;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,OAAO,CACV,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAC/D,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAA;IAEnC,KAAK,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACzD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,IAAI,CAClB,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,EAEjE,aAAa,EAAE,aAAa,CAAC,iBAAiB,CAAC,EAC/C,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,GACvC,GAAG,CAAC,GAAG,CA8GT;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,CACV,iBAAiB,SAAS,UAAU,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,IAC/D;QACF,OAAO,EAAE,MAAM,CAAA;QACf,iBAAiB,EAAE,OAAO,CAAC,OAAO,CAAA;QAClC,iBAAiB,EAAE,iBAAiB,GAAG,UAAU,CAAC,OAAO,CAAA;KAC1D,CAAA;IAED,KAAK,SAAS,GACV,aAAa,CAAC,MAAM,CAAC,SAAS,GAC9B,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;GAuBG;AACH,wBAAgB,QAAQ,CAAC,aAAa,EAAE,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,MAAM,CAiD1E;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAY,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC/C;AAED;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,wBAAgB,KAAK,CAAC,aAAa,EAAE,aAAa,GAAG,GAAG,CA6BvD;AAED,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,KAAY,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC1E"}