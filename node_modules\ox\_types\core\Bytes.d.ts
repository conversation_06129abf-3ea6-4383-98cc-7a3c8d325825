import * as Errors from './Errors.js';
import * as Hex from './Hex.js';
import * as internal from './internal/bytes.js';
import * as internal_hex from './internal/hex.js';
/** Root type for a Bytes array. */
export type Bytes = Uint8Array;
/**
 * Asserts if the given value is {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.assert('abc')
 * // @error: Bytes.InvalidBytesTypeError:
 * // @error: Value `"abc"` of type `string` is an invalid Bytes value.
 * // @error: Bytes values must be of type `Uint8Array`.
 * ```
 *
 * @param value - Value to assert.
 */
export declare function assert(value: unknown): asserts value is Bytes;
export declare namespace assert {
    type ErrorType = InvalidBytesTypeError | Errors.GlobalErrorType;
}
/**
 * Concatenates two or more {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const bytes = Bytes.concat(
 *   Bytes.from([1]),
 *   Bytes.from([69]),
 *   Bytes.from([420, 69]),
 * )
 * // @log: Uint8Array [ 1, 69, 420, 69 ]
 * ```
 *
 * @param values - Values to concatenate.
 * @returns Concatenated {@link ox#Bytes.Bytes}.
 */
export declare function concat(...values: readonly Bytes[]): Bytes;
export declare namespace concat {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Instantiates a {@link ox#Bytes.Bytes} value from a `Uint8Array`, a hex string, or an array of unsigned 8-bit integers.
 *
 * :::tip
 *
 * To instantiate from a **Boolean**, **String**, or **Number**, use one of the following:
 *
 * - `Bytes.fromBoolean`
 *
 * - `Bytes.fromString`
 *
 * - `Bytes.fromNumber`
 *
 * :::
 *
 * @example
 * ```ts twoslash
 * // @noErrors
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.from([255, 124, 5, 4])
 * // @log: Uint8Array([255, 124, 5, 4])
 *
 * const data = Bytes.from('0xdeadbeef')
 * // @log: Uint8Array([222, 173, 190, 239])
 * ```
 *
 * @param value - Value to convert.
 * @returns A {@link ox#Bytes.Bytes} instance.
 */
export declare function from(value: Hex.Hex | Bytes | readonly number[]): Bytes;
export declare namespace from {
    type ErrorType = fromHex.ErrorType | fromArray.ErrorType | Errors.GlobalErrorType;
}
/**
 * Converts an array of unsigned 8-bit integers into {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromArray([255, 124, 5, 4])
 * // @log: Uint8Array([255, 124, 5, 4])
 * ```
 *
 * @param value - Value to convert.
 * @returns A {@link ox#Bytes.Bytes} instance.
 */
export declare function fromArray(value: readonly number[] | Uint8Array): Bytes;
export declare namespace fromArray {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Encodes a boolean value into {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromBoolean(true)
 * // @log: Uint8Array([1])
 * ```
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromBoolean(true, { size: 32 })
 * // @log: Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1])
 * ```
 *
 * @param value - Boolean value to encode.
 * @param options - Encoding options.
 * @returns Encoded {@link ox#Bytes.Bytes}.
 */
export declare function fromBoolean(value: boolean, options?: fromBoolean.Options): Uint8Array;
export declare namespace fromBoolean {
    type Options = {
        /** Size of the output bytes. */
        size?: number | undefined;
    };
    type ErrorType = internal.assertSize.ErrorType | padLeft.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a {@link ox#Hex.Hex} value into {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421')
 * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33])
 * ```
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromHex('0x48656c6c6f20776f726c6421', { size: 32 })
 * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
 * ```
 *
 * @param value - {@link ox#Hex.Hex} value to encode.
 * @param options - Encoding options.
 * @returns Encoded {@link ox#Bytes.Bytes}.
 */
export declare function fromHex(value: Hex.Hex, options?: fromHex.Options): Bytes;
export declare namespace fromHex {
    type Options = {
        /** Size of the output bytes. */
        size?: number | undefined;
    };
    type ErrorType = internal_hex.assertSize.ErrorType | Hex.padRight.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a number value into {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromNumber(420)
 * // @log: Uint8Array([1, 164])
 * ```
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromNumber(420, { size: 4 })
 * // @log: Uint8Array([0, 0, 1, 164])
 * ```
 *
 * @param value - Number value to encode.
 * @param options - Encoding options.
 * @returns Encoded {@link ox#Bytes.Bytes}.
 */
export declare function fromNumber(value: bigint | number, options?: fromNumber.Options | undefined): Uint8Array;
export declare namespace fromNumber {
    type Options = Hex.fromNumber.Options;
    type ErrorType = Hex.fromNumber.ErrorType | fromHex.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a string into {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromString('Hello world!')
 * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 119, 111, 114, 108, 100, 33])
 * ```
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.fromString('Hello world!', { size: 32 })
 * // @log: Uint8Array([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])
 * ```
 *
 * @param value - String to encode.
 * @param options - Encoding options.
 * @returns Encoded {@link ox#Bytes.Bytes}.
 */
export declare function fromString(value: string, options?: fromString.Options): Bytes;
export declare namespace fromString {
    type Options = {
        /** Size of the output bytes. */
        size?: number | undefined;
    };
    type ErrorType = internal.assertSize.ErrorType | padRight.ErrorType | Errors.GlobalErrorType;
}
/**
 * Checks if two {@link ox#Bytes.Bytes} values are equal.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.isEqual(Bytes.from([1]), Bytes.from([1]))
 * // @log: true
 *
 * Bytes.isEqual(Bytes.from([1]), Bytes.from([2]))
 * // @log: false
 * ```
 *
 * @param bytesA - First {@link ox#Bytes.Bytes} value.
 * @param bytesB - Second {@link ox#Bytes.Bytes} value.
 * @returns `true` if the two values are equal, otherwise `false`.
 */
export declare function isEqual(bytesA: Bytes, bytesB: Bytes): boolean;
export declare namespace isEqual {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Pads a {@link ox#Bytes.Bytes} value to the left with zero bytes until it reaches the given `size` (default: 32 bytes).
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.padLeft(Bytes.from([1]), 4)
 * // @log: Uint8Array([0, 0, 0, 1])
 * ```
 *
 * @param value - {@link ox#Bytes.Bytes} value to pad.
 * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.
 * @returns Padded {@link ox#Bytes.Bytes} value.
 */
export declare function padLeft(value: Bytes, size?: number | undefined): padLeft.ReturnType;
export declare namespace padLeft {
    type ReturnType = internal.pad.ReturnType;
    type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType;
}
/**
 * Pads a {@link ox#Bytes.Bytes} value to the right with zero bytes until it reaches the given `size` (default: 32 bytes).
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.padRight(Bytes.from([1]), 4)
 * // @log: Uint8Array([1, 0, 0, 0])
 * ```
 *
 * @param value - {@link ox#Bytes.Bytes} value to pad.
 * @param size - Size to pad the {@link ox#Bytes.Bytes} value to.
 * @returns Padded {@link ox#Bytes.Bytes} value.
 */
export declare function padRight(value: Bytes, size?: number | undefined): padRight.ReturnType;
export declare namespace padRight {
    type ReturnType = internal.pad.ReturnType;
    type ErrorType = internal.pad.ErrorType | Errors.GlobalErrorType;
}
/**
 * Generates random {@link ox#Bytes.Bytes} of the specified length.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const bytes = Bytes.random(32)
 * // @log: Uint8Array([... x32])
 * ```
 *
 * @param length - Length of the random {@link ox#Bytes.Bytes} to generate.
 * @returns Random {@link ox#Bytes.Bytes} of the specified length.
 */
export declare function random(length: number): Bytes;
export declare namespace random {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Retrieves the size of a {@link ox#Bytes.Bytes} value.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.size(Bytes.from([1, 2, 3, 4]))
 * // @log: 4
 * ```
 *
 * @param value - {@link ox#Bytes.Bytes} value.
 * @returns Size of the {@link ox#Bytes.Bytes} value.
 */
export declare function size(value: Bytes): number;
export declare namespace size {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Returns a section of a {@link ox#Bytes.Bytes} value given a start/end bytes offset.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.slice(
 *   Bytes.from([1, 2, 3, 4, 5, 6, 7, 8, 9]),
 *   1,
 *   4,
 * )
 * // @log: Uint8Array([2, 3, 4])
 * ```
 *
 * @param value - The {@link ox#Bytes.Bytes} value.
 * @param start - Start offset.
 * @param end - End offset.
 * @param options - Slice options.
 * @returns Sliced {@link ox#Bytes.Bytes} value.
 */
export declare function slice(value: Bytes, start?: number | undefined, end?: number | undefined, options?: slice.Options): Bytes;
export declare namespace slice {
    type Options = {
        /** Asserts that the sliced value is the same size as the given start/end offsets. */
        strict?: boolean | undefined;
    };
    type ErrorType = internal.assertStartOffset.ErrorType | internal.assertEndOffset.ErrorType | Errors.GlobalErrorType;
}
/**
 * Decodes a {@link ox#Bytes.Bytes} into a bigint.
 *
 * @example
 * ```ts
 * import { Bytes } from 'ox'
 *
 * Bytes.toBigInt(Bytes.from([1, 164]))
 * // @log: 420n
 * ```
 *
 * @param bytes - The {@link ox#Bytes.Bytes} to decode.
 * @param options - Decoding options.
 * @returns Decoded bigint.
 */
export declare function toBigInt(bytes: Bytes, options?: toBigInt.Options): bigint;
export declare namespace toBigInt {
    type Options = {
        /** Whether or not the number of a signed representation. */
        signed?: boolean | undefined;
        /** Size of the bytes. */
        size?: number | undefined;
    };
    type ErrorType = Hex.fromBytes.ErrorType | Hex.toBigInt.ErrorType | Errors.GlobalErrorType;
}
/**
 * Decodes a {@link ox#Bytes.Bytes} into a boolean.
 *
 * @example
 * ```ts
 * import { Bytes } from 'ox'
 *
 * Bytes.toBoolean(Bytes.from([1]))
 * // @log: true
 * ```
 *
 * @param bytes - The {@link ox#Bytes.Bytes} to decode.
 * @param options - Decoding options.
 * @returns Decoded boolean.
 */
export declare function toBoolean(bytes: Bytes, options?: toBoolean.Options): boolean;
export declare namespace toBoolean {
    type Options = {
        /** Size of the bytes. */
        size?: number | undefined;
    };
    type ErrorType = internal.assertSize.ErrorType | trimLeft.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a {@link ox#Bytes.Bytes} value into a {@link ox#Hex.Hex} value.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.toHex(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))
 * // '0x48656c6c6f20576f726c6421'
 * ```
 *
 * @param value - The {@link ox#Bytes.Bytes} to decode.
 * @param options - Options.
 * @returns Decoded {@link ox#Hex.Hex} value.
 */
export declare function toHex(value: Bytes, options?: toHex.Options): Hex.Hex;
export declare namespace toHex {
    type Options = {
        /** Size of the bytes. */
        size?: number | undefined;
    };
    type ErrorType = Hex.fromBytes.ErrorType | Errors.GlobalErrorType;
}
/**
 * Decodes a {@link ox#Bytes.Bytes} into a number.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.toNumber(Bytes.from([1, 164]))
 * // @log: 420
 * ```
 */
export declare function toNumber(bytes: Bytes, options?: toNumber.Options): number;
export declare namespace toNumber {
    type Options = {
        /** Whether or not the number of a signed representation. */
        signed?: boolean | undefined;
        /** Size of the bytes. */
        size?: number | undefined;
    };
    type ErrorType = Hex.fromBytes.ErrorType | Hex.toNumber.ErrorType | Errors.GlobalErrorType;
}
/**
 * Decodes a {@link ox#Bytes.Bytes} into a string.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * const data = Bytes.toString(Bytes.from([72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33]))
 * // @log: 'Hello world'
 * ```
 *
 * @param bytes - The {@link ox#Bytes.Bytes} to decode.
 * @param options - Options.
 * @returns Decoded string.
 */
export declare function toString(bytes: Bytes, options?: toString.Options): string;
export declare namespace toString {
    type Options = {
        /** Size of the bytes. */
        size?: number | undefined;
    };
    type ErrorType = internal.assertSize.ErrorType | trimRight.ErrorType | Errors.GlobalErrorType;
}
/**
 * Trims leading zeros from a {@link ox#Bytes.Bytes} value.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.trimLeft(Bytes.from([0, 0, 0, 0, 1, 2, 3]))
 * // @log: Uint8Array([1, 2, 3])
 * ```
 *
 * @param value - {@link ox#Bytes.Bytes} value.
 * @returns Trimmed {@link ox#Bytes.Bytes} value.
 */
export declare function trimLeft(value: Bytes): Bytes;
export declare namespace trimLeft {
    type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType;
}
/**
 * Trims trailing zeros from a {@link ox#Bytes.Bytes} value.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.trimRight(Bytes.from([1, 2, 3, 0, 0, 0, 0]))
 * // @log: Uint8Array([1, 2, 3])
 * ```
 *
 * @param value - {@link ox#Bytes.Bytes} value.
 * @returns Trimmed {@link ox#Bytes.Bytes} value.
 */
export declare function trimRight(value: Bytes): Bytes;
export declare namespace trimRight {
    type ErrorType = internal.trim.ErrorType | Errors.GlobalErrorType;
}
/**
 * Checks if the given value is {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.validate('0x')
 * // @log: false
 *
 * Bytes.validate(Bytes.from([1, 2, 3]))
 * // @log: true
 * ```
 *
 * @param value - Value to check.
 * @returns `true` if the value is {@link ox#Bytes.Bytes}, otherwise `false`.
 */
export declare function validate(value: unknown): value is Bytes;
export declare namespace validate {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Thrown when the bytes value cannot be represented as a boolean.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.toBoolean(Bytes.from([5]))
 * // @error: Bytes.InvalidBytesBooleanError: Bytes value `[5]` is not a valid boolean.
 * // @error: The bytes array must contain a single byte of either a `0` or `1` value.
 * ```
 */
export declare class InvalidBytesBooleanError extends Errors.BaseError {
    readonly name = "Bytes.InvalidBytesBooleanError";
    constructor(bytes: Bytes);
}
/**
 * Thrown when a value cannot be converted to bytes.
 *
 * @example
 * ```ts twoslash
 * // @noErrors
 * import { Bytes } from 'ox'
 *
 * Bytes.from('foo')
 * // @error: Bytes.InvalidBytesTypeError: Value `foo` of type `string` is an invalid Bytes value.
 * ```
 */
export declare class InvalidBytesTypeError extends Errors.BaseError {
    readonly name = "Bytes.InvalidBytesTypeError";
    constructor(value: unknown);
}
/**
 * Thrown when a size exceeds the maximum allowed size.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.fromString('Hello World!', { size: 8 })
 * // @error: Bytes.SizeOverflowError: Size cannot exceed `8` bytes. Given size: `12` bytes.
 * ```
 */
export declare class SizeOverflowError extends Errors.BaseError {
    readonly name = "Bytes.SizeOverflowError";
    constructor({ givenSize, maxSize }: {
        givenSize: number;
        maxSize: number;
    });
}
/**
 * Thrown when a slice offset is out-of-bounds.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.slice(Bytes.from([1, 2, 3]), 4)
 * // @error: Bytes.SliceOffsetOutOfBoundsError: Slice starting at offset `4` is out-of-bounds (size: `3`).
 * ```
 */
export declare class SliceOffsetOutOfBoundsError extends Errors.BaseError {
    readonly name = "Bytes.SliceOffsetOutOfBoundsError";
    constructor({ offset, position, size, }: {
        offset: number;
        position: 'start' | 'end';
        size: number;
    });
}
/**
 * Thrown when a the padding size exceeds the maximum allowed size.
 *
 * @example
 * ```ts twoslash
 * import { Bytes } from 'ox'
 *
 * Bytes.padLeft(Bytes.fromString('Hello World!'), 8)
 * // @error: [Bytes.SizeExceedsPaddingSizeError: Bytes size (`12`) exceeds padding size (`8`).
 * ```
 */
export declare class SizeExceedsPaddingSizeError extends Errors.BaseError {
    readonly name = "Bytes.SizeExceedsPaddingSizeError";
    constructor({ size, targetSize, type, }: {
        size: number;
        targetSize: number;
        type: 'Hex' | 'Bytes';
    });
}
//# sourceMappingURL=Bytes.d.ts.map