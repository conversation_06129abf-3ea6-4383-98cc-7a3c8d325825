{"version": 3, "file": "wallet.d.ts", "sourceRoot": "", "sources": ["../../../../core/internal/rpcSchemas/wallet.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,OAAO,MAAM,kBAAkB,CAAA;AAChD,OAAO,KAAK,KAAK,GAAG,MAAM,cAAc,CAAA;AACxC,OAAO,KAAK,KAAK,SAAS,MAAM,oBAAoB,CAAA;AACpD,OAAO,KAAK,KAAK,kBAAkB,MAAM,6BAA6B,CAAA;AACtE,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,aAAa,CAAA;AAE1C;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI;AACjC;;;;GAIG;AACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,qBAAqB,CAAA;QAC7B,MAAM,CAAC,EAAE,SAAS,CAAA;KACnB,CAAA;IACD,UAAU,EAAE,SAAS,OAAO,CAAC,OAAO,EAAE,CAAA;CACvC;AACH;;;;;;;;GAQG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,wBAAwB,CAAA;QAChC,MAAM,EAAE,CAAC,qBAAqB,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;KACzC,CAAA;IACD,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;CACpB;AACH;;;;;;;;GAQG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,qBAAqB,CAAA;QAC7B,MAAM,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAA;KAC9C,CAAA;IACD,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;CACpB;AACH;;;;;;;;GAQG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,qBAAqB,CAAA;QAC7B,MAAM,EAAE,CAAC,OAAO,EAAE,kBAAkB,CAAC,GAAG,CAAC,CAAA;KAC1C,CAAA;IACD,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;CACpB;AACH;;;;;;;;GAQG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,sBAAsB,CAAA;QAC9B,MAAM,EAAE;YACN,iCAAiC;YACjC,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,gFAAgF;YAChF,OAAO,EAAE,MAAM;SAChB,CAAA;KACF,CAAA;IACD,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;CACpB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,eAAe,CAAA;QACvB,MAAM,EAAE;YACN,mBAAmB;YACnB,IAAI,EAAE,GAAG,CAAC,GAAG;YACb,iCAAiC;YACjC,OAAO,EAAE,OAAO,CAAC,OAAO;SACzB,CAAA;KACF,CAAA;IACD,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;CACpB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,yBAAyB,CAAA;QACjC,MAAM,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,gCAAgC,CAAC,CAAC,CAAA;KAC3D,CAAA;IACD,UAAU,EAAE,IAAI,CAAA;CACjB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,uBAAuB,CAAA;QAC/B,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,CAAA;KAClB,CAAA;IACD,UAAU,EAAE,OAAO,CAAC,8BAA8B,CAAC,CAAA;CACpD;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,wBAAwB,CAAA;QAChC,MAAM,CAAC,EACH,SAAS,EAAE,GACX,SAAS,CAAC,OAAO,CAAC,OAAO,GAAG,SAAS,CAAC,GACtC,SAAS;YACP,OAAO,CAAC,OAAO,GAAG,SAAS;YAC3B,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,SAAS;SAC/B,GACD,SAAS,CAAA;KACd,CAAA;IACD,UAAU,EAAE,OAAO,CAAC,qBAAqB,CAAC,CAAA;CAC3C;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,uBAAuB,CAAA;QAC/B,MAAM,CAAC,EAAE,SAAS,CAAA;KACnB,CAAA;IACD,UAAU,EAAE,SAAS,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAA;CACjD;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,yBAAyB,CAAA;QACjC,MAAM,CAAC,EAAE,CAAC,gCAAgC,CAAC,CAAA;KAC5C,CAAA;IACD,UAAU,EAAE,OAAO,CAAC,gCAAgC,CAAC,CAAA;CACtD;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,2BAA2B,CAAA;QACnC,MAAM,EAAE,CAAC,WAAW,EAAE;YAAE,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;SAAE,CAAC,CAAA;KAC7D,CAAA;IACD,UAAU,EAAE,SAAS,OAAO,CAAC,gBAAgB,CAAC,EAAE,CAAA;CACjD;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,0BAA0B,CAAA;QAClC,MAAM,EAAE,CAAC,WAAW,EAAE;YAAE,YAAY,EAAE,MAAM,CAAC,MAAM,EAAE,GAAG,CAAC,CAAA;SAAE,CAAC,CAAA;KAC7D,CAAA;IACD,UAAU,EAAE,IAAI,CAAA;CACjB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,kBAAkB,CAAA;QAC1B,MAAM,EAAE,OAAO,CAAC,yBAAyB,CAAC,CAAA;KAC3C,CAAA;IACD,UAAU,EAAE,yBAAyB,CAAA;CACtC;AACH;;;;;GAKG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,wBAAwB,CAAA;QAChC,MAAM,EAAE,CAAC,MAAM,CAAC,CAAA;KACjB,CAAA;IACD,UAAU,EAAE,SAAS,CAAA;CACtB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,4BAA4B,CAAA;QACpC,MAAM,EAAE,CAAC,KAAK,EAAE;YAAE,OAAO,EAAE,MAAM,CAAA;SAAE,CAAC,CAAA;KACrC,CAAA;IACD,UAAU,EAAE,IAAI,CAAA;CACjB;AACH;;;;GAIG;GACD;IACE,OAAO,EAAE;QACP,MAAM,EAAE,mBAAmB,CAAA;QAC3B,MAAM,EAAE,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAA;KAC9C,CAAA;IACD,UAAU,EAAE,OAAO,CAAA;CACpB,CACJ,CAAA;AAED;;;GAGG;AACH,KAAK,gCAAgC,GAAG;IACtC,uCAAuC;IACvC,OAAO,EAAE,MAAM,CAAA;IACf,sBAAsB;IACtB,SAAS,EAAE,MAAM,CAAA;IACjB,qCAAqC;IACrC,cAAc,CAAC,EACX;QACE,IAAI,EAAE,MAAM,CAAA;QACZ,MAAM,EAAE,MAAM,CAAA;QACd,QAAQ,EAAE,MAAM,CAAA;KACjB,GACD,SAAS,CAAA;IACb,OAAO,EAAE,SAAS,MAAM,EAAE,CAAA;IAC1B,iBAAiB,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,SAAS,CAAA;IACjD,QAAQ,CAAC,EAAE,SAAS,MAAM,EAAE,GAAG,SAAS,CAAA;CACzC,CAAA;AAED;;;GAGG;AACH,KAAK,kBAAkB,GAAG;IACxB,CAAC,UAAU,EAAE,MAAM,GAAG,GAAG,CAAA;CAC1B,CAAA;AAED;;;GAGG;AACH,KAAK,qBAAqB,GAAG;IAC3B,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,kBAAkB,CAAA;CACvC,CAAA;AAED;;;GAGG;AACH,KAAK,8BAA8B,GAAG;IACpC,MAAM,EAAE,OAAO,CAAA;IACf,YAAY,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAA;IAC7C,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;IAChB,EAAE,EAAE,MAAM,CAAA;IACV,QAAQ,CAAC,EACL,SAAS;QACP,IAAI,EAAE;YACJ,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;YAChB,IAAI,EAAE,GAAG,CAAC,GAAG,CAAA;YACb,MAAM,EAAE,SAAS,GAAG,CAAC,GAAG,EAAE,CAAA;SAC3B,EAAE,CAAA;QACH,MAAM,EAAE,GAAG,CAAC,GAAG,CAAA;QACf,SAAS,EAAE,GAAG,CAAC,GAAG,CAAA;QAClB,WAAW,EAAE,GAAG,CAAC,GAAG,CAAA;QACpB,OAAO,EAAE,GAAG,CAAC,GAAG,CAAA;QAChB,eAAe,EAAE,GAAG,CAAC,GAAG,CAAA;KACzB,EAAE,GACH,SAAS,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,EAAE,MAAM,CAAA;CAChB,CAAA;AAED;;;GAGG;AACH,KAAK,sBAAsB,GAAG;IAC5B,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,GAAG,CAAA;CACX,CAAA;AAED;;;GAGG;AACH,KAAK,gBAAgB,GAAG;IACtB,OAAO,EAAE,SAAS,sBAAsB,EAAE,CAAA;IAC1C,IAAI,EAAE,MAAM,CAAA;IACZ,EAAE,EAAE,MAAM,CAAA;IACV,OAAO,EAAE,UAAU,MAAM,EAAE,GAAG,WAAW,MAAM,EAAE,CAAA;IACjD,gBAAgB,EAAE,cAAc,GAAG,MAAM,CAAA;CAC1C,CAAA;AAED;;;GAGG;AACH,KAAK,gCAAgC,GAAG;IACtC,MAAM,CAAC,EACH;QACE,IAAI,EAAE,MAAM,CAAA;QACZ,IAAI,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;KAC3B,GACD,SAAS,CAAA;IACb,WAAW,EAAE,SAAS;QACpB,IAAI,EAAE,OAAO,CAAA;QACb,QAAQ,EAAE,SAAS;YACjB,IAAI,EAAE,OAAO,CAAA;YACb,IAAI,EAAE,MAAM,CAAA;SACb,EAAE,CAAA;QACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC9B,IAAI,EAAE,MAAM,CAAA;KACb,EAAE,CAAA;IACH,MAAM,EAAE,MAAM,CAAA;CACf,CAAA;AAED;;;GAGG;AACH,KAAK,gCAAgC,GAAG;IACtC,MAAM,EAAE,MAAM,CAAA;IACd,OAAO,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,SAAS,CAAA;IACnC,WAAW,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAChC,kBAAkB,EAAE,SAAS;QAC3B,IAAI,EAAE,OAAO,CAAA;QACb,QAAQ,EAAE,SAAS;YACjB,IAAI,EAAE,OAAO,CAAA;YACb,IAAI,EAAE,MAAM,CAAA;SACb,EAAE,CAAA;QACH,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;QAC9B,IAAI,EAAE,MAAM,CAAA;KACb,EAAE,CAAA;IACH,kBAAkB,EAAE,MAAM,CAAA;IAC1B,UAAU,CAAC,EACP;QACE,aAAa,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,SAAS,CAAA;QACzC,eAAe,CAAC,EAAE,KAAK,MAAM,EAAE,GAAG,SAAS,CAAA;KAC5C,GACD,SAAS,CAAA;CACd,CAAA;AAED;;;GAGG;AACH,KAAK,yBAAyB,GAAG;IAC/B;QACE,cAAc,EAAE,OAAO,CAAA;QACvB,KAAK,EAAE,SAAS;YACd,YAAY,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAA;YAC7C,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;YAChC,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;YAC1B,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;SAC5B,EAAE,CAAA;QACH,YAAY,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAA;QAC7C,OAAO,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;QAC7B,EAAE,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACvB,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;QAClC,OAAO,EAAE,MAAM,CAAA;KAChB;CACF,CAAA;AAED;;;GAGG;AACH,KAAK,yBAAyB,GAAG;IAC/B,YAAY,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAA;IAC7C,EAAE,EAAE,MAAM,CAAA;CACX,CAAA;AAED;;;GAGG;AACH,KAAK,0BAA0B,GAAG;IAChC,kBAAkB;IAClB,IAAI,EAAE,OAAO,CAAA;IACb,OAAO,EAAE;QACP,wCAAwC;QACxC,OAAO,EAAE,MAAM,CAAA;QACf,wDAAwD;QACxD,MAAM,EAAE,MAAM,CAAA;QACd,mCAAmC;QACnC,QAAQ,EAAE,MAAM,CAAA;QAChB,qCAAqC;QACrC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC3B,CAAA;CACF,CAAA"}