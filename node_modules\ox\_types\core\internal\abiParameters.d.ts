import type { AbiParameter, AbiParameterKind, AbiParameterToPrimitiveType, AbiParametersToPrimitiveTypes } from 'abitype';
import * as AbiParameters from '../AbiParameters.js';
import * as Address from '../Address.js';
import * as Bytes from '../Bytes.js';
import * as Errors from '../Errors.js';
import * as Hex from '../Hex.js';
import type * as Cursor from './cursor.js';
import type { Compute, IsNarrowable, UnionToIntersection } from './types.js';
/** @internal */
export type ParameterToPrimitiveType<abiParameter extends AbiParameter | {
    name: string;
    type: unknown;
}, abiParameterKind extends AbiParameterKind = AbiParameterKind> = AbiParameterToPrimitiveType<abiParameter, abiParameterKind>;
/** @internal */
export type PreparedParameter = {
    dynamic: boolean;
    encoded: Hex.Hex;
};
/** @internal */
export type ToObject<parameters extends readonly AbiParameter[], kind extends AbiParameterKind = AbiParameterKind> = IsNarrowable<parameters, AbiParameters.AbiParameters> extends true ? Compute<UnionToIntersection<{
    [index in keyof parameters]: parameters[index] extends {
        name: infer name extends string;
    } ? {
        [key in name]: AbiParameterToPrimitiveType<parameters[index], kind>;
    } : {
        [key in index]: AbiParameterToPrimitiveType<parameters[index], kind>;
    };
}[number]>> : unknown;
/** @internal */
export type ToPrimitiveTypes<abiParameters extends readonly AbiParameter[], abiParameterKind extends AbiParameterKind = AbiParameterKind> = AbiParametersToPrimitiveTypes<abiParameters, abiParameterKind>;
/** @internal */
export type Tuple = ParameterToPrimitiveType<TupleAbiParameter>;
/** @internal */
export declare function decodeParameter(cursor: Cursor.Cursor, param: AbiParameters.Parameter, options: {
    checksumAddress?: boolean | undefined;
    staticPosition: number;
}): any[];
export declare namespace decodeParameter {
    type ErrorType = decodeArray.ErrorType | decodeTuple.ErrorType | decodeAddress.ErrorType | decodeBool.ErrorType | decodeBytes.ErrorType | decodeNumber.ErrorType | decodeString.ErrorType | AbiParameters.InvalidTypeError | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeAddress(cursor: Cursor.Cursor, options?: {
    checksum?: boolean | undefined;
}): (number | `0x${string}`)[];
export declare namespace decodeAddress {
    type ErrorType = Hex.fromBytes.ErrorType | Bytes.slice.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeArray(cursor: Cursor.Cursor, param: AbiParameters.Parameter, options: {
    checksumAddress?: boolean | undefined;
    length: number | null;
    staticPosition: number;
}): (number | unknown[])[];
export declare namespace decodeArray {
    type ErrorType = Bytes.toNumber.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeBool(cursor: Cursor.Cursor): (number | boolean)[];
export declare namespace decodeBool {
    type ErrorType = Bytes.toBoolean.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeBytes(cursor: Cursor.Cursor, param: AbiParameters.Parameter, { staticPosition }: {
    staticPosition: number;
}): (string | number)[];
export declare namespace decodeBytes {
    type ErrorType = Hex.fromBytes.ErrorType | Bytes.toNumber.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeNumber(cursor: Cursor.Cursor, param: AbiParameters.Parameter): (number | bigint)[];
export declare namespace decodeNumber {
    type ErrorType = Bytes.toNumber.ErrorType | Bytes.toBigInt.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export type TupleAbiParameter = AbiParameters.Parameter & {
    components: readonly AbiParameters.Parameter[];
};
/** @internal */
export declare function decodeTuple(cursor: Cursor.Cursor, param: TupleAbiParameter, options: {
    checksumAddress?: boolean | undefined;
    staticPosition: number;
}): any[];
export declare namespace decodeTuple {
    type ErrorType = Bytes.toNumber.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function decodeString(cursor: Cursor.Cursor, { staticPosition }: {
    staticPosition: number;
}): (string | number)[];
export declare namespace decodeString {
    type ErrorType = Bytes.toNumber.ErrorType | Bytes.toString.ErrorType | Bytes.trimLeft.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function prepareParameters<const parameters extends AbiParameters.AbiParameters>({ checksumAddress, parameters, values, }: {
    checksumAddress?: boolean | undefined;
    parameters: parameters;
    values: parameters extends AbiParameters.AbiParameters ? ToPrimitiveTypes<parameters> : never;
}): PreparedParameter[];
/** @internal */
export declare namespace prepareParameters {
    type ErrorType = prepareParameter.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function prepareParameter<const parameter extends AbiParameters.Parameter>({ checksumAddress, parameter: parameter_, value, }: {
    parameter: parameter;
    value: parameter extends AbiParameters.Parameter ? ParameterToPrimitiveType<parameter> : never;
    checksumAddress?: boolean | undefined;
}): PreparedParameter;
/** @internal */
export declare namespace prepareParameter {
    type ErrorType = encodeArray.ErrorType | encodeTuple.ErrorType | encodeAddress.ErrorType | encodeBoolean.ErrorType | encodeBytes.ErrorType | encodeString.ErrorType | AbiParameters.InvalidTypeError | Errors.GlobalErrorType;
}
/** @internal */
export declare function encode(preparedParameters: PreparedParameter[]): Hex.Hex;
/** @internal */
export declare namespace encode {
    type ErrorType = Hex.concat.ErrorType | Hex.fromNumber.ErrorType | Hex.size.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeAddress(value: Hex.Hex, options: {
    checksum: boolean;
}): PreparedParameter;
/** @internal */
export declare namespace encodeAddress {
    type ErrorType = Address.assert.ErrorType | Hex.padLeft.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeArray<const parameter extends AbiParameters.Parameter>(value: ParameterToPrimitiveType<parameter>, options: {
    checksumAddress?: boolean | undefined;
    length: number | null;
    parameter: parameter;
}): PreparedParameter;
/** @internal */
export declare namespace encodeArray {
    type ErrorType = AbiParameters.InvalidArrayError | AbiParameters.ArrayLengthMismatchError | Hex.concat.ErrorType | Hex.fromNumber.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeBytes(value: Hex.Hex, { type }: {
    type: string;
}): PreparedParameter;
/** @internal */
export declare namespace encodeBytes {
    type ErrorType = Hex.padLeft.ErrorType | Hex.padRight.ErrorType | Hex.fromNumber.ErrorType | Hex.slice.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeBoolean(value: boolean): PreparedParameter;
/** @internal */
export declare namespace encodeBoolean {
    type ErrorType = Hex.padLeft.ErrorType | Hex.fromBoolean.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeNumber(value: number, { signed, size }: {
    signed: boolean;
    size: number;
}): PreparedParameter;
/** @internal */
export declare namespace encodeNumber {
    type ErrorType = Hex.fromNumber.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeString(value: string): PreparedParameter;
/** @internal */
export declare namespace encodeString {
    type ErrorType = Hex.fromNumber.ErrorType | Hex.padRight.ErrorType | Hex.slice.ErrorType | Hex.size.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function encodeTuple<const parameter extends AbiParameters.Parameter & {
    components: readonly AbiParameters.Parameter[];
}>(value: ParameterToPrimitiveType<parameter>, options: {
    checksumAddress?: boolean | undefined;
    parameter: parameter;
}): PreparedParameter;
/** @internal */
export declare namespace encodeTuple {
    type ErrorType = Hex.concat.ErrorType | Errors.GlobalErrorType;
}
/** @internal */
export declare function getArrayComponents(type: string): [length: number | null, innerType: string] | undefined;
/** @internal */
export declare function hasDynamicChild(param: AbiParameters.Parameter): any;
//# sourceMappingURL=abiParameters.d.ts.map