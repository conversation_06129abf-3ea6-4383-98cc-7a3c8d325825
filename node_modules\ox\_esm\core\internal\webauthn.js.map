{"version": 3, "file": "webauthn.js", "sourceRoot": "", "sources": ["../../../core/internal/webauthn.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAA;AAEzC,OAAO,KAAK,GAAG,MAAM,WAAW,CAAA;AAChC,OAAO,KAAK,SAAS,MAAM,iBAAiB,CAAA;AAC5C,OAAO,EAAE,6BAA6B,EAAE,MAAM,oBAAoB,CAAA;AAuJlE;;;;GAIG;AACH,MAAM,UAAU,kBAAkB,CAAC,KAAiB;IAClD,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IACtC,MAAM,KAAK,GAAG,OAAO,GAAG,EAAE,CAAA;IAC1B,MAAM,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAA;IAE9D,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC,CAAA;IAC5D,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAErD,OAAO;QACL,CAAC;QACD,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;KAChD,CAAA;AACH,CAAC;AAED;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAC5C,QAA0C;IAE1C,IAAI,CAAC;QACH,MAAM,eAAe,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAA;QAC/C,IAAI,CAAC,eAAe;YAAE,MAAM,IAAI,6BAA6B,EAAE,CAAA;QAE/D,6FAA6F;QAC7F,MAAM,cAAc,GAAG,IAAI,UAAU,CAAC,eAAe,CAAC,CAAA;QACtD,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAC7C,MAAM,EACN,IAAI,UAAU,CAAC,cAAc,CAAC,EAC9B;YACE,IAAI,EAAE,OAAO;YACb,UAAU,EAAE,OAAO;YACnB,IAAI,EAAE,SAAS;SAChB,EACD,IAAI,EACJ,CAAC,QAAQ,CAAC,CACX,CAAA;QACD,MAAM,SAAS,GAAG,IAAI,UAAU,CAC9B,MAAM,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAChD,CAAA;QACD,OAAO,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAClC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,0FAA0F;QAC1F,mEAAmE;QACnE,gFAAgF;QAChF,IAAK,KAAe,CAAC,OAAO,KAAK,oCAAoC;YACnE,MAAM,KAAK,CAAA;QAEb,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAA;QACvD,MAAM,gBAAgB,GAAG,IAAI,CAAA;QAC7B,MAAM,UAAU,GAAG,IAAI,CAAA;QAEvB,MAAM,SAAS,GAAG,CAAC,GAAW,EAAE,EAAE;YAChC,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,CAAC,GAAG,EAAE,UAAU,EAAE,gBAAgB,CAAC,CAAC,CAAA;YACtE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE;gBACtD,IAAI,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC;oBACrD,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,CAAA;YAChC,MAAM,IAAI,6BAA6B,EAAE,CAAA;QAC3C,CAAC,CAAA;QAED,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;QAC9B,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,CAAA;QAE9B,OAAO,SAAS,CAAC,IAAI,CACnB,IAAI,UAAU,CAAC;YACb,IAAI;YACJ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC;YAChD,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,gBAAgB,CAAC;SACjD,CAAC,CACH,CAAA;IACH,CAAC;AACH,CAAC"}