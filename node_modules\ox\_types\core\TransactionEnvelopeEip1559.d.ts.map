{"version": 3, "file": "TransactionEnvelopeEip1559.d.ts", "sourceRoot": "", "sources": ["../../core/TransactionEnvelopeEip1559.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,UAAU,MAAM,iBAAiB,CAAA;AAC7C,OAAO,KAAK,OAAO,MAAM,cAAc,CAAA;AACvC,OAAO,KAAK,KAAK,MAAM,MAAM,aAAa,CAAA;AAC1C,OAAO,KAAK,IAAI,MAAM,WAAW,CAAA;AACjC,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,GAAG,MAAM,UAAU,CAAA;AAC/B,OAAO,KAAK,SAAS,MAAM,gBAAgB,CAAA;AAC3C,OAAO,KAAK,mBAAmB,MAAM,0BAA0B,CAAA;AAC/D,OAAO,KAAK,EACV,MAAM,EACN,OAAO,EACP,SAAS,EACT,cAAc,EACf,MAAM,qBAAqB,CAAA;AAE5B,MAAM,MAAM,0BAA0B,CACpC,MAAM,SAAS,OAAO,GAAG,OAAO,EAChC,UAAU,GAAG,MAAM,EACnB,UAAU,GAAG,MAAM,EACnB,IAAI,SAAS,MAAM,GAAG,IAAI,IACxB,OAAO,CACT,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,GAAG;IAC/D,4BAA4B;IAC5B,UAAU,CAAC,EAAE,UAAU,CAAC,UAAU,GAAG,SAAS,CAAA;IAC9C,gFAAgF;IAChF,YAAY,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IACrC,yCAAyC;IACzC,oBAAoB,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;CAC9C,CACF,CAAA;AAED,MAAM,MAAM,GAAG,CAAC,MAAM,SAAS,OAAO,GAAG,OAAO,IAAI,0BAA0B,CAC5E,MAAM,EACN,GAAG,CAAC,GAAG,EACP,GAAG,CAAC,GAAG,EACP,KAAK,CACN,CAAA;AAED,MAAM,MAAM,UAAU,GAAG,GAAG,cAAc,GAAG,MAAM,EAAE,CAAA;AAErD,eAAO,MAAM,cAAc,QAAkB,CAAA;AAC7C,MAAM,MAAM,cAAc,GAAG,OAAO,cAAc,CAAA;AAElD,MAAM,MAAM,MAAM,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAA;AAErD,eAAO,MAAM,IAAI,WAAqB,CAAA;AACtC,MAAM,MAAM,IAAI,GAAG,OAAO,IAAI,CAAA;AAE9B;;;;;;;;;;;;;;;;;;;GAmBG;AACH,wBAAgB,MAAM,CACpB,QAAQ,EAAE,SAAS,CAAC,0BAA0B,EAAE,MAAM,CAAC,QAiBxD;AAED,MAAM,CAAC,OAAO,WAAW,MAAM,CAAC;IAC9B,KAAK,SAAS,GACV,OAAO,CAAC,MAAM,CAAC,SAAS,GACxB,mBAAmB,CAAC,mBAAmB,GACvC,mBAAmB,CAAC,kBAAkB,GACtC,mBAAmB,CAAC,mBAAmB,GACvC,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;GAoBG;AACH,wBAAgB,WAAW,CACzB,UAAU,EAAE,UAAU,GACrB,OAAO,CAAC,0BAA0B,CAAC,CAsErC;AAED,MAAM,CAAC,OAAO,WAAW,WAAW,CAAC;IACnC,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2EG;AACH,wBAAgB,IAAI,CAClB,KAAK,CAAC,QAAQ,SACV,cAAc,CAAC,0BAA0B,EAAE,MAAM,CAAC,GAClD,UAAU,EACd,KAAK,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,EAEnE,QAAQ,EACJ,QAAQ,GACR,cAAc,CAAC,0BAA0B,EAAE,MAAM,CAAC,GAClD,UAAU,EACd,OAAO,GAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAM,GACpC,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,SAAS,CAAC,CActC;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,IACxE;QACE,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;KACxD,CAAA;IAEH,KAAK,UAAU,CACb,QAAQ,SACJ,cAAc,CAAC,0BAA0B,EAAE,MAAM,CAAC,GAClD,GAAG,CAAC,GAAG,GAAG,0BAA0B,GAAG,GAAG,CAAC,GAAG,EAClD,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,SAAS,GAAG,SAAS,IAC3D,OAAO,CACT,QAAQ,SAAS,GAAG,CAAC,GAAG,GACpB,0BAA0B,GAC1B,MAAM,CACJ,QAAQ,EACR,CAAC,SAAS,SAAS,SAAS,CAAC,SAAS,GAAG,QAAQ,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,GAAG;QACnE,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAA;KACzB,CACF,CACN,CAAA;IAED,KAAK,SAAS,GACV,WAAW,CAAC,SAAS,GACrB,MAAM,CAAC,SAAS,GAChB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;GA2BG;AACH,wBAAgB,cAAc,CAC5B,QAAQ,EAAE,0BAA0B,GACnC,cAAc,CAAC,UAAU,CAE3B;AAED,MAAM,CAAC,OAAO,WAAW,cAAc,CAAC;IACtC,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG,CAAA;IAEzB,KAAK,SAAS,GAAG,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACzD;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6BG;AACH,wBAAgB,IAAI,CAAC,OAAO,SAAS,OAAO,GAAG,KAAK,EAClD,QAAQ,EAAE,0BAA0B,CAAC,OAAO,SAAS,IAAI,GAAG,KAAK,GAAG,IAAI,CAAC,EACzE,OAAO,GAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAM,GAClC,IAAI,CAAC,UAAU,CAejB;AAED,MAAM,CAAC,OAAO,WAAW,IAAI,CAAC;IAC5B,KAAK,OAAO,CAAC,OAAO,SAAS,OAAO,GAAG,KAAK,IAAI;QAC9C,mEAAmE;QACnE,OAAO,CAAC,EAAE,OAAO,GAAG,OAAO,GAAG,SAAS,CAAA;KACxC,CAAA;IAED,KAAK,UAAU,GAAG,GAAG,CAAC,GAAG,CAAA;IAEzB,KAAK,SAAS,GACV,IAAI,CAAC,SAAS,CAAC,SAAS,GACxB,SAAS,CAAC,SAAS,GACnB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAiDG;AACH,wBAAgB,SAAS,CACvB,QAAQ,EAAE,SAAS,CAAC,0BAA0B,EAAE,MAAM,CAAC,EACvD,OAAO,GAAE,SAAS,CAAC,OAAY,GAC9B,UAAU,CAkCZ;AAED,MAAM,CAAC,OAAO,WAAW,SAAS,CAAC;IACjC,KAAK,OAAO,GAAG;QACb,kEAAkE;QAClE,SAAS,CAAC,EAAE,SAAS,CAAC,SAAS,GAAG,SAAS,CAAA;KAC5C,CAAA;IAED,KAAK,SAAS,GACV,MAAM,CAAC,SAAS,GAChB,GAAG,CAAC,UAAU,CAAC,SAAS,GACxB,SAAS,CAAC,OAAO,CAAC,SAAS,GAC3B,GAAG,CAAC,MAAM,CAAC,SAAS,GACpB,GAAG,CAAC,OAAO,CAAC,SAAS,GACrB,MAAM,CAAC,eAAe,CAAA;CAC3B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BG;AACH,wBAAgB,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,0BAA0B,EAAE,MAAM,CAAC,GAAG,GAAG,CA2B7E;AAED,MAAM,CAAC,OAAO,WAAW,KAAK,CAAC;IAC7B,KAAY,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CAC7E;AAED;;;;;;;;;;;;;;;;;GAiBG;AACH,wBAAgB,QAAQ,CACtB,QAAQ,EAAE,SAAS,CAAC,0BAA0B,EAAE,MAAM,CAAC,WAQxD;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,KAAK,SAAS,GAAG,MAAM,CAAC,eAAe,CAAA;CACxC"}