{"version": 3, "file": "UserOperation.js", "sourceRoot": "", "sources": ["../../erc4337/UserOperation.ts"], "names": [], "mappings": ";;AA6LA,oBAaC;AA+CD,0BAgBC;AAqCD,wCAOC;AAqCD,oBAmHC;AA4CD,4BAiDC;AA6BD,sBA6BC;AApmBD,0DAAyD;AAGzD,wCAAuC;AACvC,sCAAqC;AACrC,kDAAiD;AAwLjD,SAAgB,IAAI,CAIlB,aAA4C,EAC5C,UAAmC,EAAE;IAErC,MAAM,SAAS,GAAG,CAAC,GAAG,EAAE;QACtB,IAAI,CAAC,OAAO,CAAC,SAAS;YAAE,OAAO,SAAS,CAAA;QACxC,IAAI,OAAO,OAAO,CAAC,SAAS,KAAK,QAAQ;YAAE,OAAO,OAAO,CAAC,SAAS,CAAA;QACnE,OAAO,SAAS,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;IAC3C,CAAC,CAAC,EAAE,CAAA;IACJ,OAAO,EAAE,GAAG,aAAa,EAAE,SAAS,EAAW,CAAA;AACjD,CAAC;AA+CD,SAAgB,OAAO,CAAC,GAAQ;IAC9B,OAAO;QACL,GAAG,GAAG;QACN,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;QACtC,YAAY,EAAE,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC;QACtC,oBAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACtD,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;QACxB,kBAAkB,EAAE,MAAM,CAAC,GAAG,CAAC,kBAAkB,CAAC;QAClD,oBAAoB,EAAE,MAAM,CAAC,GAAG,CAAC,oBAAoB,CAAC;QACtD,GAAG,CAAC,GAAG,CAAC,uBAAuB,IAAI;YACjC,uBAAuB,EAAE,MAAM,CAAC,GAAG,CAAC,uBAAuB,CAAC;SAC7D,CAAC;QACF,GAAG,CAAC,GAAG,CAAC,6BAA6B,IAAI;YACvC,6BAA6B,EAAE,MAAM,CAAC,GAAG,CAAC,6BAA6B,CAAC;SACzE,CAAC;KACc,CAAA;AACpB,CAAC;AAqCD,SAAgB,cAAc,CAG5B,aAA+C,EAC/C,OAAkD;IAElD,OAAO,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;AACrC,CAAC;AAqCD,SAAgB,IAAI,CAGlB,aAA+C,EAC/C,OAAwC;IAExC,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,GAAG,OAAO,CAAA;IACjE,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,WAAW,EACX,YAAY,EACZ,oBAAoB,EACpB,KAAK,EACL,SAAS,EACT,gBAAgB,EAChB,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,kBAAkB,EAClB,MAAM,EACN,oBAAoB,GACrB,GAAG,aAA8B,CAAA;IAElC,MAAM,YAAY,GAAG,CAAC,GAAG,EAAE;QACzB,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,OAAO,aAAa,CAAC,MAAM,CACzB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC;gBAChC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACxB,YAAY;gBACZ,oBAAoB;gBACpB,kBAAkB;gBAClB,YAAY;gBACZ,oBAAoB;gBACpB,IAAI,CAAC,SAAS,CAAC,gBAAgB,IAAI,IAAI,CAAC;aACzC,CACF,CAAA;QACH,CAAC;QAED,IAAI,iBAAiB,KAAK,KAAK,EAAE,CAAC;YAChC,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CACjC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,EACrD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAC9C,CAAA;YACD,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CACxB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC,EACrD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,EAAE,CAAC,CAC9C,CAAA;YACD,MAAM,eAAe,GAAG,IAAI,CAAC,SAAS,CACpC,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CACjE,CAAA;YACD,MAAM,uBAAuB,GAAG,IAAI,CAAC,SAAS,CAC5C,SAAS;gBACP,CAAC,CAAC,GAAG,CAAC,MAAM,CACR,SAAS,EACT,GAAG,CAAC,OAAO,CACT,GAAG,CAAC,UAAU,CAAC,6BAA6B,IAAI,CAAC,CAAC,EAClD,EAAE,CACH,EACD,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,uBAAuB,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,EAC7D,aAAa,IAAI,IAAI,CACtB;gBACH,CAAC,CAAC,IAAI,CACT,CAAA;YAED,OAAO,aAAa,CAAC,MAAM,CACzB;gBACE,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;gBACnB,EAAE,IAAI,EAAE,SAAS,EAAE;aACpB,EACD;gBACE,MAAM;gBACN,KAAK;gBACL,eAAe;gBACf,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;gBACxB,gBAAgB;gBAChB,kBAAkB;gBAClB,OAAO;gBACP,uBAAuB;aACxB,CACF,CAAA;QACH,CAAC;QAED,MAAM,IAAI,KAAK,CAAC,sBAAsB,iBAAiB,kBAAkB,CAAC,CAAA;IAC5E,CAAC,CAAC,EAAE,CAAA;IAEJ,OAAO,IAAI,CAAC,SAAS,CACnB,aAAa,CAAC,MAAM,CAClB,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC/D,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,iBAAiB,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CACnE,CACF,CAAA;AACH,CAAC;AA4CD,SAAgB,QAAQ,CAAC,aAAyC;IAChE,MAAM,EACJ,YAAY,EACZ,QAAQ,EACR,OAAO,EACP,WAAW,EACX,oBAAoB,EACpB,YAAY,EACZ,KAAK,EACL,SAAS,EACT,aAAa,EACb,uBAAuB,EACvB,6BAA6B,EAC7B,MAAM,EACN,SAAS,EACT,oBAAoB,GACrB,GAAG,aAAa,CAAA;IAEjB,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CACjC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAC3D,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CACpD,CAAA;IACD,MAAM,QAAQ,GACZ,OAAO,IAAI,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAA;IAClE,MAAM,OAAO,GAAG,GAAG,CAAC,MAAM,CACxB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,oBAAoB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAC3D,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,CACpD,CAAA;IACD,MAAM,gBAAgB,GAAG,SAAS;QAChC,CAAC,CAAC,GAAG,CAAC,MAAM,CACR,SAAS,EACT,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,6BAA6B,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EACpE,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,uBAAuB,IAAI,EAAE,CAAC,EAAE,EAAE,CAAC,EAC9D,aAAa,IAAI,IAAI,CACtB;QACH,CAAC,CAAC,IAAI,CAAA;IACR,MAAM,kBAAkB,GAAG,aAAa,CAAC,kBAAkB,IAAI,EAAE,CAAA;IAEjE,OAAO;QACL,gBAAgB;QAChB,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,KAAK;QACL,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,SAAS;KACV,CAAA;AACH,CAAC;AA6BD,SAAgB,KAAK,CAAC,aAA4B;IAChD,MAAM,GAAG,GAAG,EAAS,CAAA;IAErB,GAAG,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;IACrC,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,CAAA;IAC7D,GAAG,CAAC,YAAY,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,YAAY,CAAC,CAAA;IAC7D,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAA;IAC7E,GAAG,CAAC,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IAC/C,GAAG,CAAC,kBAAkB,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,CAAC,CAAA;IACzE,GAAG,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAA;IACjC,GAAG,CAAC,oBAAoB,GAAG,GAAG,CAAC,UAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC,CAAA;IAE7E,IAAI,aAAa,CAAC,OAAO;QAAE,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC,OAAO,CAAA;IAC9D,IAAI,aAAa,CAAC,WAAW;QAAE,GAAG,CAAC,WAAW,GAAG,aAAa,CAAC,WAAW,CAAA;IAC1E,IAAI,aAAa,CAAC,QAAQ;QAAE,GAAG,CAAC,QAAQ,GAAG,aAAa,CAAC,QAAQ,CAAA;IACjE,IAAI,aAAa,CAAC,SAAS;QAAE,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAA;IACpE,IAAI,aAAa,CAAC,aAAa;QAC7B,GAAG,CAAC,aAAa,GAAG,aAAa,CAAC,aAAa,CAAA;IACjD,IAAI,OAAO,aAAa,CAAC,uBAAuB,KAAK,QAAQ;QAC3D,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,UAAU,CAC1C,aAAa,CAAC,uBAAuB,CACtC,CAAA;IACH,IAAI,OAAO,aAAa,CAAC,6BAA6B,KAAK,QAAQ;QACjE,GAAG,CAAC,6BAA6B,GAAG,GAAG,CAAC,UAAU,CAChD,aAAa,CAAC,6BAA6B,CAC5C,CAAA;IACH,IAAI,aAAa,CAAC,SAAS;QAAE,GAAG,CAAC,SAAS,GAAG,aAAa,CAAC,SAAS,CAAA;IAEpE,OAAO,GAAG,CAAA;AACZ,CAAC"}