"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.etherlink = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.etherlink = (0, defineChain_js_1.defineChain)({
    id: 42793,
    name: 'Etherlink',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'XTZ',
    },
    rpcUrls: {
        default: { http: ['https://node.mainnet.etherlink.com'] },
    },
    blockExplorers: {
        default: {
            name: 'Etherlink',
            url: 'https://explorer.etherlink.com',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 33899,
        },
    },
});
//# sourceMappingURL=etherlink.js.map