import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const eon = /*#__PURE__*/ define<PERSON>hain({
    id: 7_332,
    name: '<PERSON><PERSON><PERSON> EON',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: '<PERSON><PERSON>',
    },
    rpcUrls: {
        default: { http: ['https://eon-rpc.horizenlabs.io/ethv1'] },
    },
    blockExplorers: {
        default: {
            name: 'EON Explorer',
            url: 'https://eon-explorer.horizenlabs.io',
        },
    },
    contracts: {},
});
//# sourceMappingURL=eon.js.map