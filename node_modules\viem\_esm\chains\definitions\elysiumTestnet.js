import { chainConfig } from '../../op-stack/chainConfig.js';
import { define<PERSON>hai<PERSON> } from '../../utils/chain/defineChain.js';
export const elysiumTestnet = /*#__PURE__*/ defineChain({
    ...chainConfig,
    id: 1338,
    name: 'Elysium Testnet',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'LAVA',
    },
    rpcUrls: {
        default: {
            http: ['https://elysium-test-rpc.vulcanforged.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Elysium testnet explorer',
            url: 'https://elysium-explorer.vulcanforged.com',
        },
    },
    testnet: true,
});
//# sourceMappingURL=elysiumTestnet.js.map