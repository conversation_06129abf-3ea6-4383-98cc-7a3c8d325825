{"version": 3, "file": "hdwallet.d.ts", "sourceRoot": "", "sources": ["../../src.ts/wallet/hdwallet.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AACH,OAAO,EAAuC,UAAU,EAAU,MAAM,oBAAoB,CAAC;AAC7F,OAAO,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AAUnD,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AAKzC,OAAO,KAAK,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AAC3D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AACtD,OAAO,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAC5D,OAAO,KAAK,EAAE,QAAQ,EAAE,MAAM,uBAAuB,CAAC;AAItD;;GAEG;AACH,eAAO,MAAM,WAAW,EAAE,MAA2B,CAAC;AAsFtD;;;;;;;GAOG;AACH,qBAAa,YAAa,SAAQ,UAAU;;IACxC;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,QAAQ,CAAC,WAAW,EAAG,MAAM,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,iBAAiB,EAAG,MAAM,CAAC;IAEpC;;;;;OAKG;IACH,QAAQ,CAAC,QAAQ,EAAG,IAAI,GAAG,QAAQ,CAAC;IAEpC;;;OAGG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,QAAQ,CAAC,IAAI,EAAG,IAAI,GAAG,MAAM,CAAC;IAE9B;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;OAEG;gBACS,KAAK,EAAE,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,QAAQ,EAAE,QAAQ,EAAE,IAAI,GAAG,QAAQ;IAerM,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,QAAQ,GAAG,YAAY;IAmBhD;;;;;;OAMG;IACG,OAAO,CAAC,QAAQ,EAAE,UAAU,GAAG,MAAM,EAAE,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,OAAO,CAAC,MAAM,CAAC;IAIlG;;;;;;;;;OASG;IACH,WAAW,CAAC,QAAQ,EAAE,UAAU,GAAG,MAAM,GAAG,MAAM;IAIlD;;;;;OAKG;IACH,IAAI,WAAW,IAAI,MAAM,CAcxB;IAED;;;OAGG;IACH,OAAO,IAAI,IAAI,IAAI;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE;IAEnC;;;;;;OAMG;IACH,MAAM,IAAI,gBAAgB;IAM1B;;OAEG;IACH,WAAW,CAAC,MAAM,EAAE,OAAO,GAAG,YAAY;IAmB1C;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,YAAY;IAiBtC;;;;;;OAMG;IACH,MAAM,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,GAAG,YAAY,GAAG,gBAAgB;IA+B5E;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,YAAY;IAQxF;;OAEG;IACH,MAAM,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,YAAY;IAKpE;;OAEG;IACH,MAAM,CAAC,UAAU,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,EAAE,QAAQ,GAAG,YAAY;IAQtG;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,GAAG,YAAY;CAGjD;AAED;;;;;;;GAOG;AACH,qBAAa,gBAAiB,SAAQ,UAAU;IAC5C;;OAEG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,QAAQ,CAAC,WAAW,EAAG,MAAM,CAAC;IAE9B;;OAEG;IACH,QAAQ,CAAC,iBAAiB,EAAG,MAAM,CAAC;IAEpC;;;OAGG;IACH,QAAQ,CAAC,SAAS,EAAG,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,QAAQ,CAAC,IAAI,EAAG,IAAI,GAAG,MAAM,CAAC;IAE9B;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;;OAGG;IACH,QAAQ,CAAC,KAAK,EAAG,MAAM,CAAC;IAExB;;OAEG;gBACS,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,iBAAiB,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,GAAG,QAAQ;IAYtL,OAAO,CAAC,QAAQ,EAAE,IAAI,GAAG,QAAQ,GAAG,gBAAgB;IAKpD;;;;;OAKG;IACH,IAAI,WAAW,IAAI,MAAM,CAiBxB;IAED;;;OAGG;IACH,OAAO,IAAI,IAAI,IAAI;QAAE,IAAI,EAAE,MAAM,CAAA;KAAE;IAEnC;;OAEG;IACH,WAAW,CAAC,MAAM,EAAE,OAAO,GAAG,gBAAgB;IAqB9C;;OAEG;IACH,UAAU,CAAC,IAAI,EAAE,MAAM,GAAG,gBAAgB;CAG7C;AAmBD;;;;;;;GAOG;AACH,wBAAgB,cAAc,CAAC,MAAM,EAAE,OAAO,GAAG,MAAM,CAItD;AAED;;;;;;;;GAQG;AACH,wBAAgB,qBAAqB,CAAC,MAAM,EAAE,OAAO,GAAG,MAAM,CAI7D"}