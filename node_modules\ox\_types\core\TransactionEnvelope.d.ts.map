{"version": 3, "file": "TransactionEnvelope.d.ts", "sourceRoot": "", "sources": ["../../core/TransactionEnvelope.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,OAAO,MAAM,cAAc,CAAA;AAC5C,OAAO,KAAK,MAAM,MAAM,aAAa,CAAA;AACrC,OAAO,KAAK,KAAK,GAAG,MAAM,UAAU,CAAA;AAEpC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,qBAAqB,CAAA;AAElD,qFAAqF;AACrF,MAAM,MAAM,IAAI,CACd,IAAI,SAAS,MAAM,GAAG,MAAM,EAC5B,MAAM,SAAS,OAAO,GAAG,OAAO,EAChC,UAAU,GAAG,MAAM,EACnB,UAAU,GAAG,MAAM,IACjB,OAAO,CACT;IACE,wBAAwB;IACxB,OAAO,EAAE,UAAU,CAAA;IACnB,8DAA8D;IAC9D,IAAI,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IAC1B,iFAAiF;IACjF,KAAK,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,SAAS,CAAA;IAC3B,iCAAiC;IACjC,IAAI,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,SAAS,CAAA;IAClC,6CAA6C;IAC7C,GAAG,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAC5B,iDAAiD;IACjD,KAAK,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAC9B,4BAA4B;IAC5B,EAAE,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,IAAI,GAAG,SAAS,CAAA;IACvC,uBAAuB;IACvB,IAAI,EAAE,IAAI,CAAA;IACV,8CAA8C;IAC9C,KAAK,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAC9B,yBAAyB;IACzB,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAC1B,yBAAyB;IACzB,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAC1B,+BAA+B;IAC/B,OAAO,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;IAChC,mEAAmE;IACnE,CAAC,CAAC,EAAE,UAAU,GAAG,SAAS,CAAA;CAC3B,GAAG,CAAC,MAAM,SAAS,IAAI,GAAG;IAAE,CAAC,EAAE,UAAU,CAAC;IAAC,CAAC,EAAE,UAAU,CAAA;CAAE,GAAG,EAAE,CAAC,CAClE,CAAA;AAED,+EAA+E;AAC/E,MAAM,MAAM,OAAO,CACjB,IAAI,SAAS,MAAM,GAAG,MAAM,EAC5B,MAAM,SAAS,OAAO,GAAG,OAAO,IAC9B,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,CAAA;AAExC,kFAAkF;AAClF,MAAM,MAAM,UAAU,CAAC,IAAI,SAAS,MAAM,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;AAEvE;;;;;;;;;;;;;GAaG;AACH,qBAAa,kBAAmB,SAAQ,MAAM,CAAC,SAAS;IACtD,SAAkB,IAAI,4CAA2C;gBACrD,EACV,MAAM,GACP,GAAE;QACD,MAAM,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KACvB;CAOP;AAED;;;;;;;;;;;;;GAaG;AACH,qBAAa,oBAAqB,SAAQ,MAAM,CAAC,SAAS;IACxD,SAAkB,IAAI,8CAA6C;gBACvD,EACV,QAAQ,GACT,GAAE;QACD,QAAQ,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KACzB;CAOP;AAED;;;;;;;;;;GAUG;AACH,qBAAa,mBAAoB,SAAQ,MAAM,CAAC,SAAS;IACvD,SAAkB,IAAI,6CAA4C;gBACtD,EAAE,OAAO,EAAE,EAAE;QAAE,OAAO,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAAE;CAO1D;AAED;;;;;;;;;;;;GAYG;AACH,qBAAa,sBAAuB,SAAQ,MAAM,CAAC,SAAS;IAC1D,SAAkB,IAAI,gDAA+C;gBACzD,EACV,UAAU,EACV,UAAU,EACV,IAAI,GACL,EAAE;QACD,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QACnC,UAAU,EAAE,GAAG,CAAC,GAAG,CAAA;QACnB,IAAI,EAAE,MAAM,CAAA;KACb;CAWF;AAED;;;;;;;;;;;;;;GAcG;AACH,qBAAa,mBAAoB,SAAQ,MAAM,CAAC,SAAS;IACvD,SAAkB,IAAI,6CAA4C;gBACtD,EACV,oBAAoB,EACpB,YAAY,GACb,GAAE;QACD,oBAAoB,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;QACzC,YAAY,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;KAC7B;CAaP"}