{"version": 3, "file": "formatting.d.ts", "sourceRoot": "", "sources": ["../../src.ts/providers/formatting.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AACpD,OAAO,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,yBAAyB,CAAC;AAMzE;;;GAGG;AACH,MAAM,WAAW,WAAW;IACxB;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAErB;;OAEG;IACH,MAAM,EAAE,MAAM,CAAC;IAEf;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;OAGG;IACH,qBAAqB,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAEtC;;;OAGG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;;OAGG;IACH,WAAW,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE5B;;;OAGG;IACH,aAAa,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE9B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;;OAGG;IACH,UAAU,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE3B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,aAAa,EAAE,IAAI,GAAG,MAAM,CAAC;IAE7B;;;OAGG;IACH,SAAS,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE1B;;OAEG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE7B;;OAEG;IACH,YAAY,EAAE,aAAa,CAAC,MAAM,GAAG,yBAAyB,CAAC,CAAC;CACnE;AAMD;;;GAGG;AACH,MAAM,WAAW,SAAS;IACtB;;OAEG;IACH,eAAe,EAAE,MAAM,CAAC;IAExB;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;;OAGG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,OAAO,EAAE,OAAO,CAAC;IAEjB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IAE9B;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,gBAAgB,EAAE,MAAM,CAAC;CAC5B;AAMD;;;GAGG;AACH,MAAM,WAAW,wBAAwB;IACrC;;;OAGG;IACH,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC;IAElB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;;OAIG;IACH,eAAe,EAAE,IAAI,GAAG,MAAM,CAAC;IAE/B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;;OAGG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC,SAAS,CAAC,CAAC;IAE/B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,WAAW,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE5B;;;OAGG;IACH,iBAAiB,EAAE,MAAM,CAAC;IAE1B;;OAEG;IACH,QAAQ,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAEzB;;OAEG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAE7B;;OAEG;IACH,iBAAiB,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAElC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAGb;;;;;OAKG;IACH,MAAM,EAAE,IAAI,GAAG,MAAM,CAAC;IAEtB;;;OAGG;IACH,IAAI,EAAE,IAAI,GAAG,MAAM,CAAC;CAEvB;AAqBD;;;GAGG;AACH,MAAM,WAAW,yBAAyB;IACtC;;OAEG;IACH,WAAW,EAAE,IAAI,GAAG,MAAM,CAAC;IAE3B;;OAEG;IACH,SAAS,EAAE,IAAI,GAAG,MAAM,CAAC;IAEzB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,EAAE,EAAE,IAAI,GAAG,MAAM,CAAC;IAElB;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;;OAGG;IACH,oBAAoB,EAAE,IAAI,GAAG,MAAM,CAAC;IAEpC;;;OAGG;IACH,YAAY,EAAE,IAAI,GAAG,MAAM,CAAC;IAE5B;;;OAGG;IACH,gBAAgB,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAEjC;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,SAAS,EAAE,SAAS,CAAC;IAErB;;OAEG;IACH,UAAU,EAAE,IAAI,GAAG,UAAU,CAAC;IAE9B;;OAEG;IACH,mBAAmB,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAE3C;;OAEG;IACH,iBAAiB,EAAE,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;CAClD"}