{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../../core/internal/types.ts"], "names": [], "mappings": "AAAA,gEAAgE;AAEhE,MAAM,MAAM,OAAO,CAAC,IAAI,IAAI;KAAG,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;CAAE,GAAG,OAAO,CAAA;AAExE,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,MAAM,CAAA;AAEnC;;;;;;;;GAQG;AACH,MAAM,MAAM,OAAO,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,GAAG;IAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAAA;CAAE,CAAA;AAE/C;;;;;;;;;;GAUG;AACH,MAAM,MAAM,MAAM,CAChB,CAAC,SAAS,SAAS,OAAO,EAAE,EAC5B,CAAC,EACD,GAAG,SAAS,SAAS,OAAO,EAAE,GAAG,EAAE,IACjC,CAAC,SAAS,SAAS,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,IAAI,SAAS,SAAS,OAAO,EAAE,CAAC,GACtE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GACb,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,CAAC,CAAC,GAC5B,MAAM,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,GACtB,SAAS,CAAC,GAAG,GAAG,CAAC,CAAA;AAErB;;;;;;;;GAQG;AACH,MAAM,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,CACtC,CAAC,CAAC,SAAS,CAAC,GAAG,IAAI,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,CAC5D,SAAS,IAAI,GACV,KAAK,GACL,IAAI,CAAA;AAER;;;;;;;;GAQG;AACH,MAAM,MAAM,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,KAAK,CAAA;AAE3D;;;;GAIG;AACH,MAAM,MAAM,OAAO,CAAC,IAAI,SAAS,MAAM,IAAI;IACzC,CAAC,UAAU,GAAG,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC;CACzC,CAAA;AAED;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,MAAM,EAAE,CAAC,CAAC,SAAS,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,SAAS;IAChE,MAAM,IAAI;IACV,GAAG,MAAM,IAAI;CACd,GACG,IAAI,SAAS,IAAI,GACf,IAAI,GACJ,EAAE,CAAC,IAAI,CAAC,GACV,KAAK,CAAA;AAET;;;;;;;;;;GAUG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,KAAK,CAAA;AAEnE;;;;GAIG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,IAAI,OAAO,SAAS,CAAC,GACxC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,GAChB,KAAK,GACL,IAAI,GACN,KAAK,CAAA;AAET,gBAAgB;AAChB,MAAM,MAAM,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAA;AAE5C;;;;;;;;;;;;;GAaG;AACH,MAAM,MAAM,aAAa,CAAC,CAAC,EAAE,QAAQ,SAAS,OAAO,IAAI,QAAQ,SAAS,IAAI,GAC1E,aAAa,CAAC,CAAC,CAAC,GAChB,CAAC,CAAA;AAEL;;;;;;;;;;GAUG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAA;AAEjD,gBAAgB;AAChB,MAAM,MAAM,YAAY,CAAC,CAAC,EAAE,CAAC,IAAI;KAC9B,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,MAAM,CAAC,GAC9B,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI,GACf,KAAK,GACL,CAAC,GACH,CAAC,GAAG,CAAC,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACxC,CAAA;AAED;;;;;;;;;;GAUG;AACH,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,CAAC,SAAS,SAAS,GAAG,KAAK,GAAG,CAAC,CAAA;AAE5D;;;;GAIG;AACH,MAAM,MAAM,IAAI,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,IAAI,IAAI,IAAI,CACpD,IAAI,EACJ,OAAO,CAAC,MAAM,IAAI,EAAE,IAAI,CAAC,CAC1B,CAAA;AAED;;;;;;;;;;GAUG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GACtD,YAAY,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAE1B,MAAM,MAAM,cAAc,CAAC,CAAC,IAAI,CAAC,GAAG,SAAS,cAAc,CAAC,CAAC,CAAC,EAAE,CAAA;AAEhE;;;;;;;;;;GAUG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,GACvD,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;AAE3B;;;;;;;;;;GAUG;AACH,MAAM,MAAM,IAAI,CACd,KAAK,SAAS,SAAS,OAAO,EAAE,EAChC,KAAK,IACH,KAAK,SAAS,SAAS,CAAC,KAAK,EAAE,GAAG,OAAO,EAAE,CAAC,GAC5C,IAAI,GACJ,KAAK,SAAS,SAAS,CAAC,OAAO,EAAE,GAAG,MAAM,IAAI,CAAC,GAC7C,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,GACjB,KAAK,CAAA;AAEX;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,gBAAgB,CAAC,QAAQ,SAAS,MAAM,GAAG,MAAM,EAAE,IAC7D,QAAQ,SAAS,MAAM,GACnB;IAEE,UAAU,QAAQ,EAAE;CACrB,GACD;KACG,GAAG,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,GAAG,CAAC,SAAS,MAAM,OAAO,SAC1D,MAAM,GACJ,UAAU,OAAO,EAAE,GACnB,KAAK;CACV,CAAA;AAEP,gBAAgB;AAChB,MAAM,MAAM,YAAY,CACtB,KAAK,EAEL,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,IACvB,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAA;AAEhF,gBAAgB;AAChB,MAAM,MAAM,WAAW,CAAC,CAAC,IAAI,mBAAmB,CAC9C,CAAC,SAAS,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,KAAK,CACxC,SAAS,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,GACvB,CAAC,GACD,KAAK,CAAA;AAET,gBAAgB;AAChB,MAAM,MAAM,mBAAmB,CAAC,KAAK,IAAI,CACvC,KAAK,SAAS,OAAO,GACjB,CAAC,GAAG,EAAE,KAAK,KAAK,CAAC,GACjB,KAAK,CACV,SAAS,CAAC,GAAG,EAAE,MAAM,CAAC,KAAK,CAAC,GACzB,CAAC,GACD,KAAK,CAAA;AAET,gBAAgB;AAChB,MAAM,MAAM,OAAO,CACjB,KAAK,EAEL,MAAM,GAAG,KAAK,IACZ,KAAK,SAAS,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,KAAK,GAAG,IAAI,CAAC,GAAG,KAAK,CAAA;AAE5E,gBAAgB;AAChB,MAAM,MAAM,YAAY,CACtB,IAAI,EACJ,OAAO,SAAS,OAAO,GAAG,SAAS,IACjC,OAAO,SAAS,IAAI,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAA;AAE7D,MAAM,MAAM,YAAY,CAAC,IAAI,IAAI;KAC9B,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,SAAS;CAC5C,CAAA;AAED,gBAAgB;AAChB,MAAM,MAAM,aAAa,CAAC,IAAI,IAAI;KAC/B,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,SAAS,CAAC;CACrD,CAAA;AAED,MAAM,MAAM,KAAK,CACf,KAAK,SAAS,MAAM,EACpB,QAAQ,SAAS,MAAM,GAAG,SAAS,GAAG,SAAS,EAE/C,IAAI,SAAS,UAAU,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,KAAK,CAAC,IAChD,KAAK,SAAS,MAAM,IAAI,GACxB,OAAO,CACL,IAAI,GAAG;KACJ,GAAG,IAAI,OAAO,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,SAAS,MAAM,GACxD,GAAG,SAAS,MAAM,QAAQ,GACxB,QAAQ,CAAC,GAAG,CAAC,GACb,SAAS,GACX,SAAS;CACd,CACF,GACD,KAAK,CAAA;AAET,gBAAgB;AAChB,MAAM,MAAM,UAAU,CAAC,IAAI,IAAI,IAAI,SAAS,IAAI,GAAG,MAAM,IAAI,GAAG,KAAK,CAAA;AAErE,gBAAgB;AAChB,MAAM,MAAM,SAAS,CAAC,IAAI,IAAI;KAC3B,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC,EAAE,SAAS;CAChC,CAAA;AAKD;;;GAGG;AACH,MAAM,MAAM,SAAS,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,IAAI,IAAI,CACrD,IAAI,EACJ,OAAO,CAAC,MAAM,IAAI,EAAE,IAAI,CAAC,CAC1B,CAAA;AAKD,gBAAgB;AAChB,MAAM,MAAM,YAAY,CAAC,IAAI,IAAI,IAAI,SAAS,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,CAAA;AAE3E,gBAAgB;AAChB,MAAM,MAAM,cAAc,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,IAAI,IAAI,SAAS,GAAG,GACpE,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,GACrB,KAAK,CAAA;AAET;;;;;;;;;GASG;AACH,MAAM,MAAM,SAAS,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,IAAI,IAAI,IAAI,SAAS,GAAG,GACnE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAChB,KAAK,CAAA;AAET;;;;;;;;;GASG;AACH,MAAM,MAAM,SAAS,CAAC,IAAI,EAAE,IAAI,SAAS,MAAM,IAAI,IAAI,IAAI,SAAS,GAAG,GACnE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,GAChB,KAAK,CAAA;AAET;;;;;;;;;;GAUG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,GAC5D,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GACf,KAAK,CAAA;AAET;;;;;;;;;;GAUG;AACH,MAAM,MAAM,eAAe,CAAC,CAAC,EAAE,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,SAAS,GAAG,GAC7D,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAChB,KAAK,CAAA"}