import * as Bytes from './Bytes.js';
import type * as Errors from './Errors.js';
import * as Hex from './Hex.js';
import * as internal from './internal/base58.js';
/**
 * Encodes a {@link ox#Bytes.Bytes} to a Base58-encoded string.
 *
 * @example
 * ```ts twoslash
 * import { Base58, Bytes } from 'ox'
 *
 * const value = Base58.fromBytes(Bytes.fromString('Hello World!'))
 * // @log: '2NEpo7TZRRrLZSi2U'
 * ```
 *
 * @param value - The byte array to encode.
 * @returns The Base58 encoded string.
 */
export declare function fromBytes(value: Bytes.Bytes): string;
export declare namespace fromBytes {
    type ErrorType = internal.from.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a {@link ox#Hex.Hex} to a Base58-encoded string.
 *
 * @example
 * ```ts twoslash
 * import { Base58, Hex } from 'ox'
 *
 * const value = Base58.fromHex(Hex.fromString('Hello World!'))
 * // @log: '2NEpo7TZRRrLZSi2U'
 * ```
 *
 * @param value - The byte array to encode.
 * @returns The Base58 encoded string.
 */
export declare function fromHex(value: Hex.Hex): string;
export declare namespace fromHex {
    type ErrorType = internal.from.ErrorType | Errors.GlobalErrorType;
}
/**
 * Encodes a string to a Base58-encoded string.
 *
 * @example
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value = Base58.fromString('Hello World!')
 * // @log: '2NEpo7TZRRrLZSi2U'
 * ```
 *
 * @param value - The string to encode.
 * @returns The Base58 encoded string.
 */
export declare function fromString(value: string): string;
export declare namespace fromString {
    type ErrorType = internal.from.ErrorType | Errors.GlobalErrorType;
}
/**
 * Decodes a Base58-encoded string to a {@link ox#Bytes.Bytes}.
 *
 * @example
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value = Base58.toBytes('2NEpo7TZRRrLZSi2U')
 * // @log: Uint8Array [ 72, 101, 108, 108, 111, 32, 87, 111, 114, 108, 100, 33 ]
 * ```
 *
 * @param value - The Base58 encoded string.
 * @returns The decoded byte array.
 */
export declare function toBytes(value: string): Bytes.Bytes;
export declare namespace toBytes {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Decodes a Base58-encoded string to {@link ox#Hex.Hex}.
 *
 * @example
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value = Base58.toHex('2NEpo7TZRRrLZSi2U')
 * // @log: '0x48656c6c6f20576f726c6421'
 * ```
 *
 * @param value - The Base58 encoded string.
 * @returns The decoded hex string.
 */
export declare function toHex(value: string): Hex.Hex;
export declare namespace toHex {
    type ErrorType = Errors.GlobalErrorType;
}
/**
 * Decodes a Base58-encoded string to a string.
 *
 * @example
 * ```ts twoslash
 * import { Base58 } from 'ox'
 *
 * const value = Base58.toString('2NEpo7TZRRrLZSi2U')
 * // @log: 'Hello World!'
 * ```
 *
 * @param value - The Base58 encoded string.
 * @returns The decoded string.
 */
export declare function toString(value: string): string;
export declare namespace toString {
    type ErrorType = Errors.GlobalErrorType;
}
//# sourceMappingURL=Base58.d.ts.map