import { define<PERSON>hain } from '../../utils/chain/defineChain.js';
export const ethernity = /*#__PURE__*/ defineChain({
    id: 183,
    name: 'Ethernity',
    nativeCurrency: {
        decimals: 18,
        name: '<PERSON><PERSON>',
        symbol: 'ETH',
    },
    rpcUrls: {
        default: { http: ['https://mainnet.ethernitychain.io'] },
    },
    blockExplorers: {
        default: {
            name: 'Ethernity Explorer',
            url: 'https://ernscan.io',
        },
    },
    contracts: {
        multicall3: {
            address: '******************************************',
            blockCreated: 0,
        },
    },
    testnet: false,
});
//# sourceMappingURL=ethernity.js.map