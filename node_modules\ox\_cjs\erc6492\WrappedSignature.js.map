{"version": 3, "file": "WrappedSignature.js", "sourceRoot": "", "sources": ["../../erc6492/WrappedSignature.ts"], "names": [], "mappings": ";;;AA4FA,wBAGC;AAuCD,oBAGC;AAyBD,0BAWC;AAiCD,sBAWC;AAwBD,4BAOC;AAvPD,0DAAyD;AAEzD,4CAA2C;AAC3C,sCAAqC;AACrC,kDAAiD;AAepC,QAAA,UAAU,GACrB,oEAA6E,CAAA;AAKlE,QAAA,mCAAmC,GAC9C,4yGAA4yG,CAAA;AAOjyG,QAAA,8BAA8B,GAAG;IAC5C;QACE,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,OAAO;aACd;SACF;QACD,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,aAAa;KACpB;IACD;QACE,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,SAAS;gBACf,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,OAAO;gBACb,IAAI,EAAE,SAAS;aAChB;YACD;gBACE,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,OAAO;aACd;SACF;QACD,OAAO,EAAE;YACP;gBACE,IAAI,EAAE,MAAM;aACb;SACF;QACD,eAAe,EAAE,YAAY;QAC7B,IAAI,EAAE,UAAU;QAChB,IAAI,EAAE,YAAY;KACnB;CACyB,CAAA;AAe5B,SAAgB,MAAM,CAAC,OAAgB;IACrC,IAAI,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,KAAK,kBAAU;QACxC,MAAM,IAAI,4BAA4B,CAAC,OAAO,CAAC,CAAA;AACnD,CAAC;AAuCD,SAAgB,IAAI,CAAC,OAAmC;IACtD,IAAI,OAAO,OAAO,KAAK,QAAQ;QAAE,OAAO,OAAO,CAAC,OAAO,CAAC,CAAA;IACxD,OAAO,OAAO,CAAA;AAChB,CAAC;AAyBD,SAAgB,OAAO,CAAC,OAAgB;IACtC,MAAM,CAAC,OAAO,CAAC,CAAA;IAEf,MAAM,CAAC,EAAE,EAAE,IAAI,EAAE,aAAa,CAAC,GAAG,aAAa,CAAC,MAAM,CACpD,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAC3C,OAAO,CACR,CAAA;IAED,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,CAAA;IAElD,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,CAAA;AAChC,CAAC;AAiCD,SAAgB,KAAK,CAAC,KAAuB;IAC3C,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,KAAK,CAAA;IAErC,OAAO,GAAG,CAAC,MAAM,CACf,aAAa,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,CAAC,EAAE;QAChE,EAAE;QACF,IAAI;QACJ,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;KAC3B,CAAC,EACF,kBAAU,CACX,CAAA;AACH,CAAC;AAwBD,SAAgB,QAAQ,CAAC,OAAgB;IACvC,IAAI,CAAC;QACH,MAAM,CAAC,OAAO,CAAC,CAAA;QACf,OAAO,IAAI,CAAA;IACb,CAAC;IAAC,MAAM,CAAC;QACP,OAAO,KAAK,CAAA;IACd,CAAC;AACH,CAAC;AAOD,MAAa,4BAA6B,SAAQ,MAAM,CAAC,SAAS;IAGhE,YAAY,OAAgB;QAC1B,KAAK,CAAC,WAAW,OAAO,8CAA8C,CAAC,CAAA;QAHvD;;;;mBAAO,+CAA+C;WAAA;IAIxE,CAAC;CACF;AAND,oEAMC"}