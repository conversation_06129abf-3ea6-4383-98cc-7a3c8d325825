{"version": 3, "file": "namehash.js", "sourceRoot": "", "sources": ["../../src.ts/hash/namehash.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EACH,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,WAAW,EAC/C,MAAM,mBAAmB,CAAC;AAG3B,OAAO,EAAE,aAAa,EAAE,MAAM,wBAAwB,CAAC;AAEvD,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;AACjC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAEd,SAAS,cAAc,CAAC,IAAgB;IACpC,cAAc,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,mCAAmC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;IACpF,OAAO,IAAI,CAAC;AAChB,CAAC;AAED,SAAS,YAAY,CAAC,IAAY;IAC9B,MAAM,KAAK,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;IAC9C,MAAM,KAAK,GAAsB,EAAG,CAAC;IAErC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;QAAE,OAAO,KAAK,CAAC;KAAE;IAExC,IAAI,IAAI,GAAG,CAAC,CAAC;IACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACnC,MAAM,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEnB,8CAA8C;QAC9C,IAAI,CAAC,KAAK,IAAI,EAAE;YACZ,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACjD,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;SAChB;KACJ;IAED,qDAAqD;IACrD,cAAc,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,mCAAmC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAEvF,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,YAAY,CAAC,IAAY;IACrC,IAAI;QACA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAAE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC;SAAE;QAC1D,OAAO,aAAa,CAAC,IAAI,CAAC,CAAC;KAC9B;IAAC,OAAO,KAAU,EAAE;QACjB,cAAc,CAAC,KAAK,EAAE,qBAAsB,KAAK,CAAC,OAAQ,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;KAChF;AACL,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,WAAW,CAAC,IAAY;IACpC,IAAI;QACA,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC;KAC5C;IAAC,OAAO,KAAK,EAAE,GAAG;IACnB,OAAO,KAAK,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,QAAQ,CAAC,IAAY;IACjC,cAAc,CAAC,OAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE,gCAAgC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE1F,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,gCAAgC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;IAE5E,IAAI,MAAM,GAAwB,KAAK,CAAC;IAExC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IACjC,OAAO,KAAK,CAAC,MAAM,EAAE;QACjB,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,CAAE,MAAM,EAAE,SAAS,CAAa,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAE,CAAC,CAAC;KAChF;IAED,OAAO,OAAO,CAAC,MAAM,CAAC,CAAC;AAC3B,CAAC;AAED;;;;;GAKG;AACH,MAAM,UAAU,SAAS,CAAC,IAAY,EAAE,UAAmB;IACvD,MAAM,MAAM,GAAG,CAAC,UAAU,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAA,CAAC,CAAC,EAAE,CAAC;IAErD,cAAc,CAAC,MAAM,IAAI,GAAG,EAAE,qCAAqC,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAEvF,OAAO,OAAO,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE;QAClD,cAAc,CAAC,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE,SAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAE,YAAa,MAAO,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;QAEjH,MAAM,KAAK,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;QAC9C,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACnB,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;QAC5B,OAAO,KAAK,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;AAChB,CAAC"}