{"version": 3, "file": "webauthn.d.ts", "sourceRoot": "", "sources": ["../../../core/internal/webauthn.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,KAAK,MAAM,MAAM,cAAc,CAAA;AAE3C,OAAO,KAAK,SAAS,MAAM,iBAAiB,CAAA;AAC5C,OAAO,EAAE,6BAA6B,EAAE,MAAM,oBAAoB,CAAA;AAElE,gBAAgB;AAChB,MAAM,MAAM,+BAA+B,GACvC,QAAQ,GACR,YAAY,GACZ,UAAU,GACV,MAAM,CAAA;AAEV,gBAAgB;AAChB,MAAM,MAAM,uBAAuB,GAAG,gBAAgB,GAAG,UAAU,CAAA;AAEnE,gBAAgB;AAChB,MAAM,MAAM,sBAAsB,GAC9B,KAAK,GACL,QAAQ,GACR,UAAU,GACV,KAAK,GACL,KAAK,CAAA;AAET,gBAAgB;AAChB,MAAM,MAAM,uBAAuB,GAAG,MAAM,CAAA;AAE5C,gBAAgB;AAChB,MAAM,MAAM,8BAA8B,GACtC,aAAa,GACb,UAAU,GACV,UAAU,GACV,QAAQ,CAAA;AAEZ,gBAAgB;AAChB,MAAM,MAAM,uBAAuB,GAAG,YAAY,CAAA;AAElD,gBAAgB;AAChB,MAAM,MAAM,sBAAsB,GAAG,aAAa,GAAG,WAAW,GAAG,UAAU,CAAA;AAE7E,gBAAgB;AAChB,MAAM,MAAM,2BAA2B,GACnC,aAAa,GACb,WAAW,GACX,UAAU,CAAA;AAEd,gBAAgB;AAChB,MAAM,MAAM,gBAAgB,GAAG;IAC7B,OAAO,EAAE,UAAU,GAAG,WAAW,CAAA;CAClC,CAAA;AAED,gBAAgB;AAChB,MAAM,MAAM,YAAY,GAAG,eAAe,GAAG,WAAW,CAAA;AAExD,gBAAgB;AAChB,MAAM,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,CAAA;AAEtE,gBAAgB;AAChB,MAAM,WAAW,oCAAoC;IACnD,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAC1B,YAAY,CAAC,EAAE,OAAO,CAAA;IACtB,GAAG,CAAC,EAAE,YAAY,CAAA;IAClB,SAAS,CAAC,EAAE,gBAAgB,CAAA;CAC7B;AAED,gBAAgB;AAChB,MAAM,WAAW,8BAA8B;IAC7C,uBAAuB,CAAC,EAAE,uBAAuB,CAAA;IACjD,kBAAkB,CAAC,EAAE,OAAO,CAAA;IAC5B,WAAW,CAAC,EAAE,sBAAsB,CAAA;IACpC,gBAAgB,CAAC,EAAE,2BAA2B,CAAA;CAC/C;AAED,gBAAgB;AAChB,MAAM,WAAW,UAAU;IACzB,QAAQ,CAAC,EAAE,EAAE,MAAM,CAAA;IACnB,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAA;CACtB;AAED,gBAAgB;AAChB,MAAM,WAAW,yBAAyB;IACxC,SAAS,CAAC,EAAE,kCAAkC,CAAA;IAC9C,MAAM,CAAC,EAAE,WAAW,CAAA;CACrB;AAED,gBAAgB;AAChB,MAAM,WAAW,wBAAwB;IACvC,SAAS,CAAC,EAAE,8BAA8B,CAAA;IAC1C,SAAS,CAAC,EAAE,iCAAiC,CAAA;IAC7C,MAAM,CAAC,EAAE,WAAW,CAAA;CACrB;AAED,gBAAgB;AAChB,MAAM,WAAW,mBAAoB,SAAQ,UAAU;IACrD,QAAQ,CAAC,uBAAuB,EAAE,MAAM,GAAG,IAAI,CAAA;IAC/C,QAAQ,CAAC,KAAK,EAAE,WAAW,CAAA;IAC3B,QAAQ,CAAC,QAAQ,EAAE,qBAAqB,CAAA;IACxC,yBAAyB,IAAI,qCAAqC,CAAA;CACnE;AAED,gBAAgB;AAChB,MAAM,WAAW,kCAAkC;IACjD,WAAW,CAAC,EAAE,+BAA+B,CAAA;IAC7C,sBAAsB,CAAC,EAAE,8BAA8B,CAAA;IACvD,SAAS,EAAE,YAAY,CAAA;IACvB,kBAAkB,CAAC,EAAE,6BAA6B,EAAE,CAAA;IACpD,UAAU,CAAC,EAAE,oCAAoC,CAAA;IACjD,gBAAgB,EAAE,6BAA6B,EAAE,CAAA;IACjD,EAAE,EAAE,2BAA2B,CAAA;IAC/B,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,IAAI,EAAE,6BAA6B,CAAA;CACpC;AAED,gBAAgB;AAChB,MAAM,WAAW,6BAA6B;IAC5C,EAAE,EAAE,YAAY,CAAA;IAChB,UAAU,CAAC,EAAE,sBAAsB,EAAE,CAAA;IACrC,IAAI,EAAE,uBAAuB,CAAA;CAC9B;AAED,gBAAgB;AAChB,MAAM,WAAW,yBAAyB;IACxC,IAAI,EAAE,MAAM,CAAA;CACb;AAED,gBAAgB;AAChB,MAAM,WAAW,6BAA6B;IAC5C,GAAG,EAAE,uBAAuB,CAAA;IAC5B,IAAI,EAAE,uBAAuB,CAAA;CAC9B;AAED,gBAAgB;AAChB,MAAM,WAAW,iCAAiC;IAChD,gBAAgB,CAAC,EAAE,6BAA6B,EAAE,CAAA;IAClD,SAAS,EAAE,YAAY,CAAA;IACvB,UAAU,CAAC,EAAE,oCAAoC,CAAA;IACjD,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,OAAO,CAAC,EAAE,MAAM,CAAA;IAChB,gBAAgB,CAAC,EAAE,2BAA2B,CAAA;CAC/C;AAED,gBAAgB;AAChB,MAAM,WAAW,2BAA4B,SAAQ,yBAAyB;IAC5E,EAAE,CAAC,EAAE,MAAM,CAAA;CACZ;AAED,gBAAgB;AAChB,MAAM,WAAW,6BACf,SAAQ,yBAAyB;IACjC,WAAW,EAAE,MAAM,CAAA;IACnB,EAAE,EAAE,YAAY,CAAA;CACjB;AAED;;;;GAIG;AACH,wBAAgB,kBAAkB,CAAC,KAAK,EAAE,UAAU;;;EAYnD;AAED;;;;;GAKG;AACH,wBAAsB,wBAAwB,CAC5C,QAAQ,EAAE,gCAAgC,GACzC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAoD9B;AAED,MAAM,CAAC,OAAO,WAAW,wBAAwB,CAAC;IAChD,KAAK,SAAS,GAAG,6BAA6B,GAAG,MAAM,CAAC,eAAe,CAAA;CACxE"}