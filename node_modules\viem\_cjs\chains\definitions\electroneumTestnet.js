"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.electroneumTestnet = void 0;
const defineChain_js_1 = require("../../utils/chain/defineChain.js");
exports.electroneumTestnet = (0, defineChain_js_1.defineChain)({
    id: 5201420,
    name: 'Electroneum Testnet',
    nativeCurrency: {
        name: 'ETN',
        symbol: 'ETN',
        decimals: 18,
    },
    rpcUrls: {
        default: {
            http: ['https://testnet-rpc.electroneum.com'],
        },
    },
    blockExplorers: {
        default: {
            name: 'Electroneum Block Explorer',
            url: 'https://blockexplorer.thesecurityteam.rocks',
        },
    },
    testnet: true,
});
//# sourceMappingURL=electroneumTestnet.js.map